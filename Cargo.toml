[package]
name = "wsstun"
version = "1.5.3"
edition = "2024"
authors = ["Yanhuang <<EMAIL>>"]
description = "A tunnel application that exchange traffic via WebSocket Secure (WSS) connections"
build = "build.rs"

[dependencies]
mimalloc = { version = "0.1.47" }
tokio = { version = "1.45", features = ["full"] }
tokio-tungstenite = { version = "0.19", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3"
url = "2.4"
log = "0.4"
env_logger = "0.10"
axum = { version = "0.8", features = ["ws"] }
clap = { version = "4.3", features = ["derive"] }
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.7"
anyhow = "1.0"
base64 = "0.21"
rustls = "0.21"
bytes = "1.10.1"
reqwest = { version = "0.11", default-features = false, features = ["cookies", "json", "rustls-tls"] }
chrono = { version = "0.4", features = ["serde"] }
prost = "0.11"
rand = "0.9.1"

[build-dependencies]
chrono = "0.4"
chrono-tz = "0.8"
prost-build = "0.11"
