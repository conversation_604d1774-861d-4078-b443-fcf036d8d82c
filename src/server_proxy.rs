//! 处理/proxy WebSocket连接
//! 使用proxy_server.rs公共逻辑，直接连接动态目标地址

use crate::common::{Result, TunnelError};
use crate::proxy_server::{connect_to_target_optimized, parse_proxy_request, ConnectionManager};
use crate::server::ServerConfig;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade}, Extension,
        Query,
    },
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::select;
use tokio::sync::{Mutex, OnceCell};

// 全局连接管理器，延迟初始化
static CONNECTION_MANAGER: OnceCell<Arc<ConnectionManager>> = OnceCell::const_new();

// 处理WebSocket连接请求for代理功能
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    info!("处理新的WebSocket连接 /proxy路由");

    // 初始化连接管理器（如果尚未初始化）
    let connection_manager = CONNECTION_MANAGER
        .get_or_init(|| async {
            Arc::new(ConnectionManager::new(
                server_config.dns_cache_size,
                server_config.dns_cache_ttl,
                server_config.connection_timeout,
            ))
        })
        .await;

    // 对于直接代理模式，目标地址从查询参数获取（可选）
    let target_from_query = params.get("target").cloned();

    if let Some(target) = target_from_query {
        info!("/proxy的目标地址（直接模式）: {}", target);
        let connection_manager = Arc::clone(connection_manager);
        let server_config = Arc::clone(&server_config);
        ws.on_upgrade(move |socket| {
            handle_direct_proxy_socket(socket, target, connection_manager, server_config)
        })
    } else {
        info!("/proxy的动态代理模式");
        let connection_manager = Arc::clone(connection_manager);
        let server_config = Arc::clone(&server_config);
        ws.on_upgrade(move |socket| {
            handle_dynamic_proxy_socket(socket, connection_manager, server_config)
        })
    }
}

// 处理直接代理WebSocket连接（已知目标地址）
async fn handle_direct_proxy_socket(
    ws: WebSocket,
    target_addr: String,
    connection_manager: Arc<ConnectionManager>,
    server_config: Arc<ServerConfig>,
) {
    info!("建立直接代理WebSocket连接，目标: {}", target_addr);

    // 直接连接到目标TCP服务
    let parts: Vec<&str> = target_addr.split(':').collect();
    if parts.len() != 2 {
        error!("无效的目标地址格式: {}", target_addr);
        return;
    }

    let host = parts[0];
    let port = match parts[1].parse::<u16>() {
        Ok(p) => p,
        Err(e) => {
            error!("无效的端口号: {}: {}", parts[1], e);
            return;
        }
    };

    match connect_to_target_optimized(&connection_manager, host, port).await {
        Ok(target_stream) => {
            info!("连接到目标: {}", target_addr);
            if let Err(e) = handle_connection(ws, target_stream, server_config).await {
                error!("处理连接时出错: {}", e);
            }
        }
        Err(e) => {
            error!("连接目标失败 {}: {}", target_addr, e);
            // WebSocket会在超出作用域时自动关闭
        }
    }
}

// 处理动态代理WebSocket连接（从WebSocket数据中解析目标地址）
async fn handle_dynamic_proxy_socket(
    ws: WebSocket,
    connection_manager: Arc<ConnectionManager>,
    server_config: Arc<ServerConfig>,
) {
    info!("建立动态代理WebSocket连接");

    // 从WebSocket接收第一个消息，应该包含客户端的代理请求
    let (mut ws_sender, mut ws_receiver) = ws.split();

    let target_stream = match ws_receiver.next().await {
        Some(Ok(Message::Binary(data))) => {
            // 解析代理协议
            let mut cursor = std::io::Cursor::new(data.clone());
            match parse_proxy_request(&mut cursor).await {
                Ok((proxy_request, response_data)) => {
                    info!(
                        "解析到代理请求: {:?}://{}:{}",
                        proxy_request.protocol,
                        proxy_request.target_host,
                        proxy_request.target_port
                    );

                    // 如果有响应数据，先发送回客户端
                    if let Some(response_bytes) = response_data {
                        if let Err(e) = ws_sender
                            .send(Message::Binary(Bytes::from(response_bytes)))
                            .await
                        {
                            error!("发送代理响应失败: {}", e);
                            return;
                        }
                    }

                    // 使用优化的连接管理器连接到目标
                    match connect_to_target_optimized(
                        &connection_manager,
                        &proxy_request.target_host,
                        proxy_request.target_port,
                    )
                    .await
                    {
                        Ok(stream) => stream,
                        Err(e) => {
                            error!("连接目标失败: {}", e);
                            return;
                        }
                    }
                }
                Err(e) => {
                    error!("解析代理请求失败: {}", e);
                    return;
                }
            }
        }
        Some(Ok(msg)) => {
            error!("期望二进制消息，收到: {:?}", msg);
            return;
        }
        Some(Err(e)) => {
            error!("接收WebSocket消息时出错: {}", e);
            return;
        }
        None => {
            warn!("WebSocket连接立即关闭");
            return;
        }
    };

    // 重新合并WebSocket以便处理连接
    let ws = ws_sender
        .reunite(ws_receiver)
        .expect("重新合并WebSocket失败");

    if let Err(e) = handle_connection(ws, target_stream, server_config).await {
        error!("处理连接时出错: {}", e);
    }
}

// 处理双向数据转发（优化版）
async fn handle_connection(
    ws: WebSocket,
    target_stream: TcpStream,
    server_config: Arc<ServerConfig>,
) -> Result<()> {
    let (ws_sink, ws_stream) = ws.split();
    let (target_reader, target_writer) = tokio::io::split(target_stream);

    let ws_sink = Arc::new(Mutex::new(ws_sink));
    let target_writer = Arc::new(Mutex::new(target_writer));
    let target_reader = Arc::new(Mutex::new(target_reader));

    // 使用配置的缓冲区大小
    let buffer_size = server_config.buffer_size;

    // WebSocket到目标TCP
    let t1 = {
        let target_writer = Arc::clone(&target_writer);
        async move {
            let mut ws_stream = ws_stream;
            while let Some(msg) = ws_stream.next().await {
                let msg = match msg {
                    Ok(msg) => msg,
                    Err(e) => {
                        error!("接收WebSocket消息时出错: {}", e);
                        return Err(TunnelError::Other(format!("WebSocket接收错误: {}", e)));
                    }
                };

                match msg {
                    Message::Binary(data) => {
                        let mut target_writer = target_writer.lock().await;
                        if let Err(e) = target_writer.write_all(&data).await {
                            error!("写入目标时出错: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                    Message::Text(data) => {
                        let mut target_writer = target_writer.lock().await;
                        if let Err(e) = target_writer.write_all(data.as_bytes()).await {
                            error!("写入目标时出错: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                    Message::Close(_) => {
                        info!("WebSocket被客户端关闭");
                        return Ok(());
                    }
                    Message::Ping(_) => debug!("收到ping"),
                    Message::Pong(_) => debug!("收到pong"),
                }
            }
            info!("WebSocket流结束");
            Ok(())
        }
    };

    // 目标TCP到WebSocket
    let t2 = {
        let ws_sink = Arc::clone(&ws_sink);
        let target_reader = Arc::clone(&target_reader);
        async move {
            let mut buffer = vec![0u8; buffer_size];
            loop {
                let n = {
                    let mut target_reader = target_reader.lock().await;
                    match target_reader.read(&mut buffer).await {
                        Ok(0) => {
                            debug!("目标TCP连接关闭 (EOF)");
                            return Ok(());
                        }
                        Ok(n) => n,
                        Err(e) => {
                            error!("从目标读取时出错: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                };

                let data = &buffer[..n];
                {
                    let mut ws_sink = ws_sink.lock().await;
                    if let Err(e) = ws_sink
                        .send(Message::Binary(Bytes::copy_from_slice(data)))
                        .await
                    {
                        error!("向WebSocket发送数据时出错: {}", e);
                        return Err(TunnelError::Other(format!("WebSocket发送错误: {}", e)));
                    }
                }
            }
        }
    };

    // 并发运行两个任务，任一结束则终止
    select! {
        result1 = t1 => {
            debug!("WebSocket->TCP任务结束: {:?}", result1);
            result1
        },
        result2 = t2 => {
            debug!("TCP->WebSocket任务结束: {:?}", result2);
            result2
        },
    }
}
