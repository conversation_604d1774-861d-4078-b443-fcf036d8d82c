//! Proxy server-side common logic
//! Provides SOCKS5 and HTTP proxy protocol handling, reusable by server_proxy.rs and hub_service.rs

use crate::common::{Result, TunnelError};
use log::{debug, error, info};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::io::AsyncReadExt;
use tokio::net::TcpStream;
use tokio::sync::RwLock;
use tokio::time::timeout;

// SOCKS5 protocol constants
const SOCKS5_VERSION: u8 = 0x05;
const SOCKS5_NO_AUTH: u8 = 0x00;
const SOCKS5_CONNECT: u8 = 0x01;
const SOCKS5_IPV4: u8 = 0x01;
const SOCKS5_DOMAIN: u8 = 0x03;
const SOCKS5_IPV6: u8 = 0x04;
const SOCKS5_SUCCESS: u8 = 0x00;

/// DNS cache entry
#[derive(Debug, Clone)]
struct DnsCacheEntry {
    ip: String,
    timestamp: Instant,
    ttl: Duration,
}

/// Connection pool entry
#[derive(Debug)]
struct PooledConnection {
    stream: TcpStream,
    last_used: Instant,
}

/// Connection pool and DNS cache manager
#[derive(Debug)]
pub struct ConnectionManager {
    dns_cache: RwLock<HashMap<String, DnsCacheEntry>>,
    connection_pool: RwLock<HashMap<String, Vec<PooledConnection>>>,
    dns_cache_size: u32,
    dns_cache_ttl: Duration,
    connection_timeout: Duration,
}

impl ConnectionManager {
    pub fn new(dns_cache_size: u32, dns_cache_ttl: u64, connection_timeout: u64) -> Self {
        Self {
            dns_cache: RwLock::new(HashMap::new()),
            connection_pool: RwLock::new(HashMap::new()),
            dns_cache_size,
            dns_cache_ttl: Duration::from_secs(dns_cache_ttl),
            connection_timeout: Duration::from_secs(connection_timeout),
        }
    }

    /// 从缓存获取或解析DNS
    async fn resolve_dns(&self, hostname: &str) -> Result<String> {
        // 先检查缓存
        {
            let cache = self.dns_cache.read().await;
            if let Some(entry) = cache.get(hostname) {
                if entry.timestamp.elapsed() < entry.ttl {
                    debug!("DNS cache hit: {} -> {}", hostname, entry.ip);
                    return Ok(entry.ip.clone());
                }
            }
        }

        // Cache miss, perform DNS resolution
        debug!("DNS cache miss, resolving: {}", hostname);
        let start = Instant::now();

        let addrs = tokio::net::lookup_host((hostname, 80))
            .await
            .map_err(|e| TunnelError::Other(format!("DNS resolution failed {}: {}", hostname, e)))?;

        let ip = addrs
            .filter_map(|addr| {
                if addr.is_ipv4() {
                    Some(addr.ip().to_string())
                } else {
                    None
                }
            })
            .next()
            .ok_or_else(|| TunnelError::Other(format!("Unable to resolve IPv4 address: {}", hostname)))?;

        let resolve_time = start.elapsed();
        info!(
            "DNS resolution completed: {} -> {} (time: {:?})",
            hostname, ip, resolve_time
        );

        // 更新缓存
        {
            let mut cache = self.dns_cache.write().await;

            // 如果缓存已满，清理过期条目
            if cache.len() >= self.dns_cache_size as usize {
                cache.retain(|_, entry| entry.timestamp.elapsed() < entry.ttl);

                // 如果仍然满，移除最旧的条目
                if cache.len() >= self.dns_cache_size as usize {
                    if let Some(oldest_key) = cache
                        .iter()
                        .min_by_key(|(_, entry)| entry.timestamp)
                        .map(|(k, _)| k.clone())
                    {
                        cache.remove(&oldest_key);
                    }
                }
            }

            cache.insert(
                hostname.to_string(),
                DnsCacheEntry {
                    ip: ip.clone(),
                    timestamp: Instant::now(),
                    ttl: self.dns_cache_ttl,
                },
            );
        }

        Ok(ip)
    }

    /// 从连接池获取连接或创建新连接
    async fn get_connection(&self, hostname: &str, port: u16) -> Result<TcpStream> {
        let target_key = format!("{}:{}", hostname, port);

        // 先尝试从连接池获取
        {
            let mut pool = self.connection_pool.write().await;
            if let Some(connections) = pool.get_mut(&target_key) {
                // 清理过期连接（超过30秒未使用）
                connections.retain(|conn| conn.last_used.elapsed() < Duration::from_secs(30));

                if let Some(pooled) = connections.pop() {
                    debug!("Getting connection from pool: {}", target_key);
                    return Ok(pooled.stream);
                }
            }
        }

        // No available connection in pool, create new connection
        debug!("Creating new connection: {}", target_key);
        self.create_new_connection(hostname, port).await
    }

    /// 创建新的TCP连接
    async fn create_new_connection(&self, hostname: &str, port: u16) -> Result<TcpStream> {
        let ip = self.resolve_dns(hostname).await?;
        let target_addr = format!("{}:{}", ip, port);

        let connect_future = TcpStream::connect(&target_addr);

        match timeout(self.connection_timeout, connect_future).await {
            Ok(Ok(stream)) => {
                debug!("成功连接到: {} (解析为: {})", hostname, target_addr);
                Ok(stream)
            }
            Ok(Err(e)) => {
                error!("连接失败 {}: {}", target_addr, e);
                Err(TunnelError::IoError(e))
            }
            Err(_) => {
                error!("连接超时: {}", target_addr);
                Err(TunnelError::Other(format!("连接超时: {}", target_addr)))
            }
        }
    }
}

#[derive(Debug, Clone)]
pub enum ProxyProtocol {
    Socks5,
    Http,
}

#[derive(Debug, Clone)]
pub struct ProxyRequest {
    pub protocol: ProxyProtocol,
    pub target_host: String,
    pub target_port: u16,
}

/// 解析代理协议并提取目标地址
/// 返回 (ProxyRequest, 是否需要发送响应数据)
pub async fn parse_proxy_request<R>(reader: &mut R) -> Result<(ProxyRequest, Option<Vec<u8>>)>
where
    R: AsyncReadExt + Unpin,
{
    // 读取第一个字节判断协议类型
    let mut first_byte = [0u8; 1];
    reader
        .read_exact(&mut first_byte)
        .await
        .map_err(TunnelError::IoError)?;

    match first_byte[0] {
        SOCKS5_VERSION => {
            debug!("SOCKS5 protocol detected");
            parse_socks5_request(reader, first_byte[0]).await
        }
        b'C' | b'G' | b'P' | b'H' => {
            debug!("HTTP proxy protocol detected");
            parse_http_request(reader, first_byte[0]).await
        }
        _ => {
            error!("Unknown proxy protocol, first byte: 0x{:02x}", first_byte[0]);
            Err(TunnelError::Other("Unknown proxy protocol".to_string()))
        }
    }
}

/// 解析SOCKS5请求
async fn parse_socks5_request<R>(
    reader: &mut R,
    first_byte: u8,
) -> Result<(ProxyRequest, Option<Vec<u8>>)>
where
    R: AsyncReadExt + Unpin,
{
    // 第一个字节已经读取，应该是0x05
    if first_byte != SOCKS5_VERSION {
        return Err(TunnelError::Other("不是有效的SOCKS5版本".to_string()));
    }

    // 读取认证方法数量
    let mut nmethods = [0u8; 1];
    reader
        .read_exact(&mut nmethods)
        .await
        .map_err(TunnelError::IoError)?;

    // 读取认证方法列表
    let mut methods = vec![0u8; nmethods[0] as usize];
    reader
        .read_exact(&mut methods)
        .await
        .map_err(TunnelError::IoError)?;

    debug!("SOCKS5认证方法: {:?}", methods);

    // 检查是否支持无认证方法
    if !methods.contains(&SOCKS5_NO_AUTH) {
        error!("客户端不支持无认证方法");
        return Err(TunnelError::Other("SOCKS5认证方法不支持".to_string()));
    }

    // 发送认证响应
    let auth_response = vec![SOCKS5_VERSION, SOCKS5_NO_AUTH];

    // 读取连接请求
    let mut request_header = [0u8; 4];
    reader
        .read_exact(&mut request_header)
        .await
        .map_err(TunnelError::IoError)?;

    if request_header[0] != SOCKS5_VERSION {
        return Err(TunnelError::Other("无效的SOCKS5请求版本".to_string()));
    }

    if request_header[1] != SOCKS5_CONNECT {
        return Err(TunnelError::Other("只支持CONNECT命令".to_string()));
    }

    // 解析目标地址
    let (target_host, target_port) = match request_header[3] {
        SOCKS5_IPV4 => {
            let mut addr_bytes = [0u8; 6]; // 4字节IP + 2字节端口
            reader
                .read_exact(&mut addr_bytes)
                .await
                .map_err(TunnelError::IoError)?;

            let ip = format!(
                "{}.{}.{}.{}",
                addr_bytes[0], addr_bytes[1], addr_bytes[2], addr_bytes[3]
            );
            let port = u16::from_be_bytes([addr_bytes[4], addr_bytes[5]]);

            (ip, port)
        }
        SOCKS5_DOMAIN => {
            let mut domain_len = [0u8; 1];
            reader
                .read_exact(&mut domain_len)
                .await
                .map_err(TunnelError::IoError)?;

            let mut domain_bytes = vec![0u8; domain_len[0] as usize];
            reader
                .read_exact(&mut domain_bytes)
                .await
                .map_err(TunnelError::IoError)?;

            let mut port_bytes = [0u8; 2];
            reader
                .read_exact(&mut port_bytes)
                .await
                .map_err(TunnelError::IoError)?;

            let domain = String::from_utf8(domain_bytes)
                .map_err(|e| TunnelError::Other(format!("无效的域名: {}", e)))?;
            let port = u16::from_be_bytes(port_bytes);

            (domain, port)
        }
        SOCKS5_IPV6 => {
            return Err(TunnelError::Other("暂不支持IPv6".to_string()));
        }
        _ => {
            return Err(TunnelError::Other("无效的地址类型".to_string()));
        }
    };

    info!("SOCKS5请求目标: {}:{}", target_host, target_port);

    // 准备成功响应，将在连接建立后发送
    let mut response = vec![SOCKS5_VERSION, SOCKS5_SUCCESS, 0x00, SOCKS5_IPV4];
    response.extend_from_slice(&[0, 0, 0, 0]); // 绑定IP 0.0.0.0
    response.extend_from_slice(&[0, 0]); // 绑定端口 0

    let mut all_response = auth_response;
    all_response.extend_from_slice(&response);

    Ok((
        ProxyRequest {
            protocol: ProxyProtocol::Socks5,
            target_host,
            target_port,
        },
        Some(all_response),
    ))
}

/// 解析HTTP代理请求
async fn parse_http_request<R>(
    reader: &mut R,
    first_byte: u8,
) -> Result<(ProxyRequest, Option<Vec<u8>>)>
where
    R: AsyncReadExt + Unpin,
{
    // 读取HTTP请求行
    let mut line_buffer = vec![first_byte];

    // 读取直到遇到\r\n
    loop {
        let mut byte = [0u8; 1];
        reader
            .read_exact(&mut byte)
            .await
            .map_err(TunnelError::IoError)?;
        line_buffer.push(byte[0]);

        if line_buffer.len() >= 2
            && line_buffer[line_buffer.len() - 2] == b'\r'
            && line_buffer[line_buffer.len() - 1] == b'\n'
        {
            break;
        }

        if line_buffer.len() > 8192 {
            return Err(TunnelError::Other("HTTP请求行过长".to_string()));
        }
    }

    // 移除\r\n
    line_buffer.truncate(line_buffer.len() - 2);

    let request_line = String::from_utf8(line_buffer)
        .map_err(|e| TunnelError::Other(format!("无效的HTTP请求行: {}", e)))?;

    debug!("HTTP请求行: {}", request_line);

    // 解析请求行
    let parts: Vec<&str> = request_line.split_whitespace().collect();
    if parts.len() < 3 {
        return Err(TunnelError::Other("无效的HTTP请求格式".to_string()));
    }

    let method = parts[0];
    let url = parts[1];

    // 解析目标地址
    let (target_host, target_port) = if method == "CONNECT" {
        // HTTPS代理 (CONNECT方法)
        if let Some(colon_pos) = url.rfind(':') {
            let host = &url[..colon_pos];
            let port_str = &url[colon_pos + 1..];
            let port = port_str
                .parse::<u16>()
                .map_err(|_| TunnelError::Other("无效的端口号".to_string()))?;
            (host.to_string(), port)
        } else {
            (url.to_string(), 443) // 默认HTTPS端口
        }
    } else {
        // HTTP代理 (GET/POST等方法)
        if url.starts_with("http://") {
            let url_without_scheme = &url[7..]; // 移除"http://"
            if let Some(slash_pos) = url_without_scheme.find('/') {
                let host_port = &url_without_scheme[..slash_pos];
                if let Some(colon_pos) = host_port.find(':') {
                    let host = &host_port[..colon_pos];
                    let port_str = &host_port[colon_pos + 1..];
                    let port = port_str
                        .parse::<u16>()
                        .map_err(|_| TunnelError::Other("无效的端口号".to_string()))?;
                    (host.to_string(), port)
                } else {
                    (host_port.to_string(), 80) // 默认HTTP端口
                }
            } else {
                // 没有路径，整个就是主机:端口
                if let Some(colon_pos) = url_without_scheme.find(':') {
                    let host = &url_without_scheme[..colon_pos];
                    let port_str = &url_without_scheme[colon_pos + 1..];
                    let port = port_str
                        .parse::<u16>()
                        .map_err(|_| TunnelError::Other("无效的端口号".to_string()))?;
                    (host.to_string(), port)
                } else {
                    (url_without_scheme.to_string(), 80)
                }
            }
        } else {
            return Err(TunnelError::Other("只支持HTTP URL".to_string()));
        }
    };

    // 读取并丢弃剩余的请求头
    let mut headers_buffer = Vec::new();
    loop {
        let mut byte = [0u8; 1];
        reader
            .read_exact(&mut byte)
            .await
            .map_err(TunnelError::IoError)?;
        headers_buffer.push(byte[0]);

        // 检查是否遇到\r\n\r\n（请求头结束）
        if headers_buffer.len() >= 4
            && headers_buffer[headers_buffer.len() - 4] == b'\r'
            && headers_buffer[headers_buffer.len() - 3] == b'\n'
            && headers_buffer[headers_buffer.len() - 2] == b'\r'
            && headers_buffer[headers_buffer.len() - 1] == b'\n'
        {
            break;
        }

        if headers_buffer.len() > 8192 {
            return Err(TunnelError::Other("HTTP请求头过长".to_string()));
        }
    }

    info!(
        "HTTP代理请求目标: {}:{} (方法: {})",
        target_host, target_port, method
    );

    // 准备响应
    let response = if method == "CONNECT" {
        Some(b"HTTP/1.1 200 Connection established\r\n\r\n".to_vec())
    } else {
        None // HTTP GET/POST请求不需要立即响应
    };

    Ok((
        ProxyRequest {
            protocol: ProxyProtocol::Http,
            target_host,
            target_port,
        },
        response,
    ))
}

/// 建立到目标服务器的连接（原始版本，保持向后兼容）
pub async fn connect_to_target(target_host: &str, target_port: u16) -> Result<TcpStream> {
    let target_addr = format!("{}:{}", target_host, target_port);
    debug!("Connecting to target address: {}", target_addr);

    TcpStream::connect(&target_addr).await.map_err(|e| {
        error!("Failed to connect to target address {}: {}", target_addr, e);
        TunnelError::TargetConnectionFailed(target_addr)
    })
}

/// 使用连接管理器连接到目标（优化版本）
pub async fn connect_to_target_optimized(
    connection_manager: &ConnectionManager,
    target_host: &str,
    target_port: u16,
) -> Result<TcpStream> {
    connection_manager
        .get_connection(target_host, target_port)
        .await
}


