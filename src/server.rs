use crate::common::{Result, TunnelError};
use crate::hub_service_forward;
use crate::hub_service_proxy;
use crate::server_forward;
use crate::server_hub::{hub_handler, start_stats_monitoring_task, ServiceHub};
use crate::server_hub_dynamic::{self, DynamicHubState};
use crate::server_mux::{self, ClientSession};
use crate::server_proxy;
use axum::{
    extract::Extension,
    http::StatusCode,
    response::{Html, IntoResponse, Json},
    routing::get,
    Router,
};
use log::info;
use serde_json::json;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;

#[derive(Debug)]
pub struct AppState {
    pub sessions: HashMap<u32, Arc<ClientSession>>,
    pub dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
}

impl AppState {
    fn new() -> Self {
        Self {
            sessions: HashMap::new(),
            dynamic_hub_state: Arc::new(tokio::sync::Mutex::new(DynamicHubState::new())),
        }
    }
}

#[derive(Debug, Clone)]
pub struct ServerConfig {
    pub listen_addr: String,
    pub max_connections: u32, // 最大并发连接数（默认1000）
    pub keep_alive: bool,     // 是否启用HTTP Keep-Alive（默认true）

    pub dns_cache_size: u32,     // DNS缓存大小（默认1000）
    pub dns_cache_ttl: u64,      // DNS缓存TTL秒（默认300）
    pub connection_timeout: u64, // 连接超时时间秒（默认10）
    pub buffer_size: usize,      // 数据缓冲区大小（默认32KB）
    pub activity_timeout: u64,   // 连接活动超时（默认90秒）
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            listen_addr: "127.0.0.1:8060".to_string(),
            max_connections: 1000,
            keep_alive: true,
            dns_cache_size: 1000,
            dns_cache_ttl: 300,
            connection_timeout: 10,
            buffer_size: 32 * 1024, // 32KB
            activity_timeout: 90,
        }
    }
}

pub async fn run_server(config: ServerConfig) -> Result<()> {
    // Parse listening address
    let addr: SocketAddr = config.listen_addr.parse().map_err(|e| {
        TunnelError::ConfigError(format!(
            "Invalid listen address: {}: {}",
            config.listen_addr, e
        ))
    })?;

    // 创建服务中心状态实例
    let service_hub = Arc::new(RwLock::new(ServiceHub::new()));
    // 创建 Mux 会话状态
    let app_state = Arc::new(RwLock::new(AppState::new()));
    let dynamic_hub_state = app_state.read().await.dynamic_hub_state.clone();

    // 启动统计监控任务
    start_stats_monitoring_task(Arc::clone(&service_hub));
    info!("Started hub statistics monitoring task");

    // 创建共享的server配置
    let server_config = Arc::new(config.clone());

    info!(
        "Server configuration - max_connections: {}, keep_alive: {}, dns_cache_size: {}, buffer_size: {}",
        config.max_connections, config.keep_alive, config.dns_cache_size, config.buffer_size
    );

    // Create router with all endpoints
    let app = Router::new()
        // WebSocket endpoints
        .route("/forward", get(server_forward::ws_handler))
        .route("/proxy", get(server_proxy::ws_handler))
        .route("/mux", get(server_mux::ws_handler))
        .route("/hub", get(hub_handler))
        .route("/hub/forward", get(hub_service_forward::ws_handler))
        .route("/hub/proxy", get(hub_service_proxy::ws_handler))
        // Dynamic tunnel endpoints
        .route("/hub/dynamic", get(server_hub_dynamic::dynamic_control_handler))
        .route("/hub/data", get(server_hub_dynamic::dynamic_data_handler))
        // HTTP endpoints
        .route("/", get(root_handler))
        .route("/health", get(health_check))
        .route("/status", get(status_handler))
        .route("/metrics", get(metrics_handler))
        // Add shared state
        .layer(Extension(service_hub))
        .layer(Extension(server_config))
        .layer(Extension(dynamic_hub_state))
        .with_state(app_state);

    // Start server
    info!("Starting server on {}", addr);
    info!("Available endpoints:");
    info!("  WebSocket: /forward, /proxy, /mux, /hub, /hub/forward, /hub/proxy, /hub/dynamic, /hub/data");
    info!("  HTTP: /, /health, /status, /metrics");

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await.map_err(|e| {
        TunnelError::IoError(std::io::Error::new(
            std::io::ErrorKind::Other,
            e.to_string(),
        ))
    })?;

    Ok(())
}

// New handler for the root path, serving HTML
async fn root_handler() -> impl IntoResponse {
    // Embed HTML template at compile time
    let html_template = include_str!("../resources/index.html");

    // Get version information
    let version = env!("CARGO_PKG_VERSION");
    let build_time = env!("BUILD_TIMESTAMP_CST");

    // Replace placeholders with actual values
    let html_content = html_template
        .replace("{{VERSION}}", version)
        .replace("{{BUILD_TIME}}", build_time);

    Html(html_content)
}

/// 健康检查端点
async fn health_check() -> impl IntoResponse {
    (
        StatusCode::OK,
        Json(json!({
            "status": "healthy",
            "timestamp": SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            "service": "wsstun-server"
        })),
    )
}

/// 服务器状态端点
async fn status_handler(
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    // 获取mux模式的服务
    let hub = service_hub.read().await;
    let mux_services = hub.list_active_services();
    
    // 获取动态隧道模式的服务
    let dynamic_state = dynamic_hub_state.lock().await;
    let dynamic_services = dynamic_state.get_available_services();
    
    // 合并所有服务，去重
    let mut all_services = mux_services;
    for service in dynamic_services {
        if !all_services.contains(&service) {
            all_services.push(service);
        }
    }
    
    let uptime = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    (
        StatusCode::OK,
        Json(json!({
            "status": "running",
            "version": env!("CARGO_PKG_VERSION"),
            "build_time": env!("BUILD_TIMESTAMP_CST"),
            "uptime_seconds": uptime,
            "config": {
                "listen_addr": server_config.listen_addr,
                "max_connections": server_config.max_connections,
                "dns_cache_size": server_config.dns_cache_size,
                "buffer_size": server_config.buffer_size
            },
            "services": {
                "active_count": all_services.len(),
                "services": all_services
            },
            "endpoints": [
                "/forward",
                "/proxy",
                "/mux",
                "/hub",
                "/hub/forward",
                "/hub/proxy",
                "/hub/dynamic",
                "/hub/data"
            ]
        })),
    )
}

/// 服务器性能指标端点
async fn metrics_handler(
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    // 获取mux模式的服务
    let hub = service_hub.read().await;
    let mux_services = hub.list_active_services();
    
    // 获取动态隧道模式的服务
    let dynamic_state = dynamic_hub_state.lock().await;
    let dynamic_services = dynamic_state.get_available_services();
    
    // 合并所有服务，去重
    let mut all_services = mux_services.clone();
    for service in dynamic_services.clone() {
        if !all_services.contains(&service) {
            all_services.push(service);
        }
    }

    // 这里可以添加更多性能指标
    // 比如从ConnectionManager获取DNS缓存和连接池统计

    (
        StatusCode::OK,
        Json(json!({
            "metrics": {
                "active_services": all_services.len(),
                "mux_services": mux_services.len(),
                "dynamic_services": dynamic_services.len(),
                "total_connections": 0, // Not implemented yet
                "dns_cache_hits": 0,    // Not implemented yet
                "dns_cache_misses": 0,  // Not implemented yet
                "connection_pool_size": 0, // Not implemented yet
            },
            "services": all_services.iter().map(|service_id| {
                let service_type = if mux_services.contains(service_id) && dynamic_services.contains(service_id) {
                    "both"
                } else if mux_services.contains(service_id) {
                    "mux"
                } else {
                    "dynamic"
                };
                
                json!({
                    "service_id": service_id,
                    "status": "active",
                    "type": service_type
                })
            }).collect::<Vec<_>>()
        })),
    )
}
