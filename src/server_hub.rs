use crate::common::{
    HubMessage, Result, TunnelError, ConnectionId, ConnectionIdGenerator,
    extract_connection_id_and_payload, add_connection_id_prefix,
};
use crate::server::ServerConfig;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
    },
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{timeout, Duration, Instant};

// Service hub state management
#[derive(Debug)]
pub struct ServiceHub {
    // Mapping from service ID to Hub Service WebSocket connection
    hub_services: HashMap<String, HubServiceState>,
    // Mapping from client connection ID to corresponding service ID
    client_to_service: HashMap<ConnectionId, String>,
    // Connection statistics
    connection_stats: ConnectionStats,
    // Connection ID generator
    connection_id_generator: ConnectionIdGenerator,
}

#[derive(Debug)]
struct HubServiceState {
    sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    // Each client connection ID corresponds to a client WebSocket receiver
    clients: HashMap<ConnectionId, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
}

#[derive(Default, Debug)]
struct ConnectionStats {
    total_hub_services: u64,
    total_clients: u64,
    active_connections: u64,
    failed_connections: u64,
}

impl ConnectionStats {
    fn new() -> Self {
        ConnectionStats {
            total_hub_services: 0,
            total_clients: 0,
            active_connections: 0,
            failed_connections: 0,
        }
    }

    fn log_stats(&self) {
        info!(
            "Connection Stats - Hub Services: {}, Clients: {}, Active: {}, Failed: {}",
            self.total_hub_services,
            self.total_clients,
            self.active_connections,
            self.failed_connections
        );
    }
}

impl ServiceHub {
    pub fn new() -> Self {
        ServiceHub {
            hub_services: HashMap::new(),
            client_to_service: HashMap::new(),
            connection_stats: ConnectionStats::new(),
            connection_id_generator: ConnectionIdGenerator::new(),
        }
    }

    // Handle client disconnection
    pub async fn handle_client_disconnect(&mut self, client_id: ConnectionId) {
        debug!("Handling client disconnect: {}", client_id);

        if let Some(service_id) = self.client_to_service.remove(&client_id) {
            debug!(
                "Client {} was connected to service {}",
                client_id, service_id
            );

            if let Some(hub_service) = self.hub_services.get_mut(&service_id) {
                let notification = HubMessage::ClientDisconnectedNotification {
                    client_connection_id: client_id,
                };

                let json = serde_json::to_string(&notification).unwrap_or_else(|e| {
                    error!("Failed to serialize disconnect notification: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = hub_service
                        .sink
                        .lock()
                        .await
                        .send(Message::Text(json.into()))
                        .await
                    {
                        error!(
                            "Failed to notify hub service about client disconnect: {}",
                            e
                        );
                    } else {
                        debug!("Notified hub service about client {} disconnect", client_id);
                    }
                }

                hub_service.clients.remove(&client_id);
                debug!(
                    "Cleaned up client {} from service {}",
                    client_id, service_id
                );
            } else {
                debug!("No hub service found for service {}", service_id);
            }

            // 更新统计
            self.connection_stats.active_connections =
                self.connection_stats.active_connections.saturating_sub(1);
            self.connection_stats.log_stats();
        } else {
            debug!("Client {} was not registered", client_id);
        }
    }

    // Handle Hub Service disconnection
    pub async fn handle_hubservice_disconnect(&mut self, service_id: &str) {
        if let Some(hub_service) = self.hub_services.remove(service_id) {

            // 通知所有连接到该服务的客户端
            let client_count = hub_service.clients.len() as u64;

            for (client_id, client_sink) in hub_service.clients {
                let notification = HubMessage::ServiceHubServiceDisconnectedNotification {
                    service_id: service_id.to_string(),
                };

                let json = serde_json::to_string(&notification).unwrap_or_else(|e| {
                    error!("Failed to serialize message: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = client_sink
                        .lock()
                        .await
                        .send(Message::Text(json.into()))
                        .await
                    {
                        error!(
                            "Failed to notify client about hub service disconnect: {}",
                            e
                        );
                    }
                }

                // 从客户端到服务的映射中移除
                self.client_to_service.remove(&client_id);
            }

            // 更新统计
            self.connection_stats.active_connections = self
                .connection_stats
                .active_connections
                .saturating_sub(client_count + 1);
            info!(
                "Hub Service for service '{}' disconnected, {} clients affected",
                service_id, client_count
            );
            self.connection_stats.log_stats();
        }
    }

    // 转发二进制消息从客户端到Hub Service
    pub async fn forward_client_to_hubservice(&self, client_id: ConnectionId, data: Vec<u8>) -> Result<()> {
        if let Some(service_id) = self.client_to_service.get(&client_id) {
            if let Some(hub_service) = self.hub_services.get(service_id) {
                let mut hub_service_sink = hub_service.sink.lock().await;

                // 添加 client_id 前缀（固定4字节，无分隔符）
                let prefixed_data = add_connection_id_prefix(client_id, &data);

                debug!(
                    "Forwarding {} bytes from client {} to service {}",
                    data.len(),
                    client_id,
                    service_id
                );

                hub_service_sink
                    .send(Message::Binary(Bytes::from(prefixed_data)))
                    .await
                    .map_err(|e| {
                        error!(
                            "Error forwarding data to hub service for service {}: {}",
                            service_id, e
                        );
                        TunnelError::Other(format!("WebSocket error sending to hub service: {}", e))
                    })?;

                Ok(())
            } else {
                error!("Hub Service not found for service: {}", service_id);
                Err(TunnelError::ServiceUnavailable(format!(
                    "Service '{}' is not available",
                    service_id
                )))
            }
        } else {
            error!("Client '{}' is not registered", client_id);
            Err(TunnelError::Other(format!(
                "Client '{}' is not registered",
                client_id
            )))
        }
    }

    // 转发二进制消息从Hub Service到客户端
    pub async fn forward_hubservice_to_client(
        &self,
        service_id: &str,
        client_id: ConnectionId,
        data: Vec<u8>,
    ) -> Result<()> {
        if let Some(hub_service) = self.hub_services.get(service_id) {
            if let Some(client_sink) = hub_service.clients.get(&client_id) {
                let mut client_sink = client_sink.lock().await;
                client_sink
                    .send(Message::Binary(Bytes::from(data)))
                    .await
                    .map_err(|e| TunnelError::Other(format!("WebSocket error: {}", e)))?;

                Ok(())
            } else {
                Err(TunnelError::Other(format!(
                    "Client '{}' is not connected to service '{}'",
                    client_id, service_id
                )))
            }
        } else {
            Err(TunnelError::ServiceUnavailable(format!(
                "Service '{}' is not available",
                service_id
            )))
        }
    }

    // 检查服务是否可用
    pub fn is_service_available(&self, service_id: &str) -> bool {
        self.hub_services.contains_key(service_id)
    }

    // 注册Hub Service
    pub fn register_hubservice(
        &mut self,
        service_id: String,
        sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    ) {
        self.hub_services.insert(
            service_id.clone(),
            HubServiceState {
                sink,
                clients: HashMap::new(),
            },
        );

        // 更新统计
        self.connection_stats.total_hub_services += 1;
        self.connection_stats.active_connections += 1;
        info!("Registered hub service for service: {}", service_id);
        self.connection_stats.log_stats();
    }

    // 注册客户端
    pub fn register_client(
        &mut self,
        client_id: ConnectionId,
        service_id: String,
        sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    ) {
        self.client_to_service
            .insert(client_id, service_id.clone());

        if let Some(hub_service) = self.hub_services.get_mut(&service_id) {
            hub_service.clients.insert(client_id, sink);

            // 更新统计
            self.connection_stats.total_clients += 1;
            self.connection_stats.active_connections += 1;
            info!(
                "Registered client {} for service: {}",
                client_id, service_id
            );
            self.connection_stats.log_stats();
        }
    }

    // 获取Hub Service的Sink用于发送通知
    pub fn get_hubservice_sink(
        &self,
        service_id: &str,
    ) -> Option<Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>> {
        self.hub_services
            .get(service_id)
            .map(|hub_service| Arc::clone(&hub_service.sink))
    }

    // 获取活跃服务列表
    pub fn list_active_services(&self) -> Vec<String> {
        self.hub_services.keys().cloned().collect()
    }

    // 生成新的连接ID
    pub fn generate_connection_id(&mut self) -> ConnectionId {
        self.connection_id_generator.next_id()
    }

    // 手动触发统计日志（用于定期监控）
    pub fn log_current_stats(&self) {
        self.connection_stats.log_stats();
    }
}

// 处理服务中心Hub模式的WebSocket连接
pub async fn hub_handler(
    ws: WebSocketUpgrade,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_hub_socket(socket, service_hub, server_config))
}

// 启动统计监控任务
pub fn start_stats_monitoring_task(service_hub: Arc<RwLock<ServiceHub>>) {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(300)); // 每5分钟记录一次统计

        loop {
            interval.tick().await;

            let hub = service_hub.read().await;
            info!("=== Hub Statistics (5min interval) ===");
            hub.log_current_stats();

            // 如果需要，可以添加更详细的统计信息
            let hub_service_count = hub.hub_services.len();
            let client_count = hub.client_to_service.len();
            info!(
                "Current active hub services: {}, clients: {}",
                hub_service_count, client_count
            );
        }
    });
}

// 处理服务中心的WebSocket连接
async fn handle_hub_socket(
    socket: WebSocket,
    service_hub: Arc<RwLock<ServiceHub>>,
    server_config: Arc<ServerConfig>,
) {
    debug!("New hub connection established");

    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));

    // 等待第一条消息来确定是服务提供者还是客户端
    if let Some(Ok(msg)) = stream.next().await {
        match msg {
            Message::Text(text) => {
                match serde_json::from_str::<HubMessage>(&text) {
                    Ok(HubMessage::RegisterService { service_id }) => {
                        // 处理Hub Service注册
                        info!("Registering hub service for service: {}", service_id);

                        let service_id_clone = service_id.clone();

                        // 发送确认消息给Hub Service
                        {
                            let ack_msg = HubMessage::ServiceRegisteredAck {
                                service_id: service_id_clone.clone(),
                            };

                            let mut sink_guard = sink.lock().await;
                            if let Err(e) = crate::common::send_hub_message_with_context(
                                &mut *sink_guard,
                                &ack_msg,
                                "service registration",
                            )
                            .await
                            {
                                error!("Failed to send service registration ack: {}", e);
                                return;
                            }
                            drop(sink_guard);

                            // 在服务中心注册Hub Service
                            let mut hub = service_hub.write().await;
                            hub.register_hubservice(service_id_clone.clone(), Arc::clone(&sink));


                        }

                        // 继续处理该Hub Service的后续消息
                        handle_hubservice_messages(service_hub, service_id_clone, stream, Arc::clone(&server_config)).await;
                    }
                    Ok(HubMessage::ConnectToServiceRequest { service_id }) => {
                        // 处理客户端连接请求
                        info!("Client requesting connection to service: {}", service_id);

                        let service_id_clone = service_id.clone();
                        let client_id = {
                            let mut hub = service_hub.write().await;
                            hub.generate_connection_id()
                        };

                        // 检查服务是否存在
                        let service_available = {
                            let hub = service_hub.read().await;
                            hub.is_service_available(&service_id_clone)
                        };

                        if service_available {
                            // 通知Hub Service有新的客户端连接
                            {
                                let hub = service_hub.read().await;
                                if let Some(hub_service_sink) =
                                    hub.get_hubservice_sink(&service_id_clone)
                                {
                                    let notification = HubMessage::NewClientNotification {
                                        client_connection_id: client_id,
                                    };

                                    let json =
                                        serde_json::to_string(&notification).unwrap_or_else(|e| {
                                            error!("Failed to serialize message: {}", e);
                                            "".to_string()
                                        });

                                    if !json.is_empty() {
                                        if let Err(e) = hub_service_sink
                                            .lock()
                                            .await
                                            .send(Message::Text(json.into()))
                                            .await
                                        {
                                            error!(
                                                "Failed to notify hub service about new client: {}",
                                                e
                                            );
                                        }
                                    }
                                }
                            }

                            // 发送确认消息给客户端
                            let ack = HubMessage::ServiceConnectionAck {
                                service_id: service_id_clone.clone(),
                                client_connection_id: client_id,
                            };

                            let json = serde_json::to_string(&ack).unwrap_or_else(|e| {
                                error!("Failed to serialize message: {}", e);
                                "".to_string()
                            });

                            if !json.is_empty() {
                                if let Err(e) =
                                    sink.lock().await.send(Message::Text(json.into())).await
                                {
                                    error!("Failed to send service connection ack: {}", e);
                                    return;
                                }
                            }

                            // 注册客户端
                            {
                                let mut hub = service_hub.write().await;
                                hub.register_client(
                                    client_id,
                                    service_id_clone.clone(),
                                    Arc::clone(&sink),
                                );
                            }

                            // 处理该客户端的后续消息
                            handle_client_messages(
                                service_hub,
                                client_id,
                                service_id_clone,
                                stream,
                            )
                            .await;
                        } else {
                            // 服务不可用，发送错误消息
                            let error_msg = HubMessage::ServiceUnavailableNotification {
                                service_id: service_id_clone.clone(),
                                reason: "Service is not registered".to_string(),
                            };

                            let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                                error!("Failed to serialize message: {}", e);
                                "".to_string()
                            });

                            if !json.is_empty() {
                                if let Err(e) =
                                    sink.lock().await.send(Message::Text(json.into())).await
                                {
                                    error!("Failed to send error notification: {}", e);
                                }
                            }

                            // 更新失败连接统计
                            {
                                let mut hub = service_hub.write().await;
                                hub.connection_stats.failed_connections += 1;
                                warn!(
                                    "Client connection failed - service '{}' not available",
                                    service_id_clone
                                );
                                hub.connection_stats.log_stats();
                            }
                        }
                    }
                    _ => {
                        error!("Invalid first message for hub connection");

                        // 发送错误消息
                        let error_msg = HubMessage::ErrorNotification {
                            message: "Invalid first message, expected RegisterService or ConnectToServiceRequest".to_string(),
                        };

                        let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                            error!("Failed to serialize message: {}", e);
                            "".to_string()
                        });

                        if !json.is_empty() {
                            if let Err(e) = sink.lock().await.send(Message::Text(json.into())).await
                            {
                                error!("Failed to send error notification: {}", e);
                            }
                        }
                    }
                }
            }
            _ => {
                error!("Expected text message as first message");

                // 发送错误消息
                let error_msg = HubMessage::ErrorNotification {
                    message: "Expected text message as first message".to_string(),
                };

                let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                    error!("Failed to serialize message: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = sink.lock().await.send(Message::Text(json.into())).await {
                        error!("Failed to send error notification: {}", e);
                    }
                }
            }
        }
    }
}

// 处理Hub Service的后续消息
async fn handle_hubservice_messages(
    service_hub: Arc<RwLock<ServiceHub>>,
    service_id: String,
    mut stream: futures_util::stream::SplitStream<WebSocket>,
    server_config: Arc<ServerConfig>,
) {
    debug!(
        "[DEBUG] handle_hubservice_messages: Started for service {}",
        service_id
    );

    let mut last_activity = Instant::now();
    let activity_timeout = Duration::from_secs(server_config.activity_timeout); // 从配置读取连接活动超时

    loop {
        let msg_result = timeout(Duration::from_secs(5), stream.next()).await;

        match msg_result {
            Ok(Some(Ok(msg))) => {
                last_activity = Instant::now(); // 更新最后活动时间

                match msg {
                    Message::Binary(data) => {
                        let (client_id, payload_slice) = match extract_connection_id_and_payload(&data) {
                            Ok((id, payload)) => (id, payload),
                            Err(e) => {
                                error!("Failed to extract connection ID from hub service message: {}", e);
                                continue;
                            }
                        };
                        
                        // 立即拷贝数据，避免在异步调用中出现数据竞争
                        let payload = payload_slice.to_vec();

                        // 异步转发
                        if let Err(e) = service_hub
                            .read()
                            .await
                            .forward_hubservice_to_client(&service_id, client_id, payload)
                            .await
                        {
                            error!(
                                "Failed to forward data from service {} to client {}: {}",
                                service_id, client_id, e
                            );
                        }
                    }
                    Message::Text(text) => {
                        // 处理控制消息，例如ClientConnectionEndedByTarget
                        match serde_json::from_str::<HubMessage>(&text) {
                            Ok(HubMessage::ClientConnectionEndedByTarget {
                                client_connection_id,
                            }) => {
                                info!(
                                    "Hub Service for service '{}' reported client connection {} ended by target",
                                    service_id, client_connection_id
                                );

                                // 检查客户端是否仍然存在，避免重复清理
                                let client_exists = {
                                    let hub = service_hub.read().await;
                                    hub.client_to_service.contains_key(&client_connection_id)
                                };

                                if !client_exists {
                                    debug!(
                                        "Client {} already disconnected, skipping cleanup",
                                        client_connection_id
                                    );
                                    continue;
                                }

                                // 增加详细日志
                                debug!(
                                    "[DEBUG] Before handle_client_disconnect: Is client {} registered? {}",
                                    client_connection_id, client_exists
                                );

                                // 从服务中心移除该客户端连接
                                service_hub
                                    .write()
                                    .await
                                    .handle_client_disconnect(client_connection_id)
                                    .await;

                                // 不再尝试向已清理的客户端发送通知，因为handle_client_disconnect已经处理了
                                debug!("[DEBUG] Client {} cleanup completed", client_connection_id);
                            }
                            Ok(HubMessage::HubServiceHeartbeat {
                                service_id: heartbeat_service_id,
                                timestamp,
                            }) => {
                                debug!(
                                    "[Heartbeat-Server] Received application heartbeat from hub service '{}' timestamp: {}",
                                    heartbeat_service_id, timestamp
                                );

                                // 验证服务ID是否匹配
                                if heartbeat_service_id != service_id {
                                    warn!(
                                        "[Heartbeat-Server] Heartbeat service ID mismatch: expected '{}', got '{}'",
                                        service_id, heartbeat_service_id
                                    );
                                }

                                // 应用层心跳不需要特殊回应，仅记录即可
                                // 这证明连接是活跃的且没有被LB拦截
                            }
                            Err(e) => {
                                error!("Failed to parse text message from hub service: {}", e);
                            }
                            _ => {
                                // 忽略其他文本消息
                                debug!("Received unhandled text message from hub service");
                            }
                        }
                    }
                    Message::Close(_) => {
                        info!("Hub Service for service '{}' disconnected", service_id);
                        debug!(
                            "[DEBUG] handle_hubservice_messages: Received Close frame for service {}",
                            service_id
                        );
                        service_hub
                            .write()
                            .await
                            .handle_hubservice_disconnect(&service_id)
                            .await;
                        break;
                    }
                    Message::Ping(payload) => {
                        let payload_str = String::from_utf8_lossy(&payload);
                        debug!(
                            "[Heartbeat-Server] Received ping from hub service for service '{}' with payload: '{}'",
                            service_id, payload_str
                        );
                        // Pong会自动发送
                    }
                    Message::Pong(payload) => {
                        let payload_str = String::from_utf8_lossy(&payload);
                        debug!(
                            "[Heartbeat-Server] Received pong from hub service for service '{}' with payload: '{}'",
                            service_id, payload_str
                        );
                    }
                }
            }
            Ok(Some(Err(e))) => {
                error!("Error reading from hub service WebSocket: {}", e);
                break;
            }
            Ok(None) => {
                debug!("Hub Service WebSocket stream ended");
                break;
            }
            Err(_) => {
                // 超时检查
                if last_activity.elapsed() > activity_timeout {
                    warn!(
                        "Hub Service for service '{}' timed out (no activity for {}s)",
                        service_id,
                        activity_timeout.as_secs()
                    );
                    break;
                } else {
                    // 短暂超时，继续等待
                    continue;
                }
            }
        }
    }

    // WebSocket流结束，处理断开连接
    debug!(
        "[DEBUG] handle_hubservice_messages: WebSocket stream ended for service {}",
        service_id
    );
    service_hub
        .write()
        .await
        .handle_hubservice_disconnect(&service_id)
        .await;
}

// 处理客户端的后续消息
async fn handle_client_messages(
    service_hub: Arc<RwLock<ServiceHub>>,
    client_id: ConnectionId,
    service_id: String,
    mut stream: futures_util::stream::SplitStream<WebSocket>,
) {
    debug!(
        "[DEBUG] handle_client_messages: Started for client {} on service {}",
        client_id, service_id
    );

    let mut is_disconnected = false; // 添加状态跟踪

    while let Some(Ok(msg)) = stream.next().await {
        match msg {
            Message::Binary(data) => {
                debug!(
                    "[DEBUG] handle_client_messages: Received binary data of size {} from client {}",
                    data.len(),
                    client_id
                );

                // 验证client_id是否仍然在服务中心注册
                let is_registered = {
                    let hub = service_hub.read().await;
                    hub.client_to_service.contains_key(&client_id)
                };

                debug!(
                    "[DEBUG] handle_client_messages: Is client {} still registered? {}",
                    client_id, is_registered
                );

                if !is_registered {
                    debug!(
                        "[DEBUG] handle_client_messages: Skipping forward for unregistered client {}",
                        client_id
                    );
                    // 如果客户端已不再注册，应该断开连接
                    break;
                }

                // 直接转发到Hub Service，forward_client_to_hubservice 会添加前缀
                if let Err(e) = service_hub
                    .read()
                    .await
                    .forward_client_to_hubservice(client_id, data.to_vec())
                    .await
                {
                    error!("Failed to forward data to hub service for client {}: {}", client_id, e);
                    
                    // 任何转发到Hub Service失败都应该断开客户端连接
                    // 这包括：WebSocket发送失败、网络错误、Hub Service断开等
                    debug!(
                        "[DEBUG] Breaking client message loop due to hub service forward error: {}",
                        e
                    );
                    break;
                }
            }
            Message::Close(_) => {
                info!(
                    "Client '{}' for service '{}' disconnected",
                    client_id, service_id
                );
                debug!(
                    "[DEBUG] handle_client_messages: Received Close frame for client {}",
                    client_id
                );
                if !is_disconnected {
                    service_hub
                        .write()
                        .await
                        .handle_client_disconnect(client_id)
                        .await;
                    is_disconnected = true;
                }
                break;
            }
            Message::Ping(payload) => {
                let payload_str = String::from_utf8_lossy(&payload);
                debug!(
                    "[Heartbeat-Server] Received ping from client '{}' for service '{}' with payload: '{}'",
                    client_id, service_id, payload_str
                );
                // Pong会自动发送
            }
            Message::Pong(payload) => {
                let payload_str = String::from_utf8_lossy(&payload);
                debug!(
                    "[Heartbeat-Server] Received pong from client '{}' for service '{}' with payload: '{}'",
                    client_id, service_id, payload_str
                );
            }
            Message::Text(text) => {
                debug!(
                    "[DEBUG] handle_client_messages: Received text message from client {}: {}",
                    client_id, text
                );
                // 客户端通常不发送文本消息，但记录一下
            }
        }
    }

    // WebSocket流结束，处理断开连接（仅在未清理的情况下）
    if !is_disconnected {
        debug!(
            "[DEBUG] handle_client_messages: WebSocket stream ended for client {}",
            client_id
        );
        service_hub
            .write()
            .await
            .handle_client_disconnect(client_id)
            .await;
    }
}


