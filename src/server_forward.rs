use crate::common::{Result, TunnelError, DEFAULT_BUFFER_SIZE};
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Query,
    },
    http::StatusCode,
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::select;

// Handle WebSocket connection request for forward functionality
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
) -> impl IntoResponse {
    info!("Handling new WebSocket connection for /forward route");

    let target_from_query = params.get("target").cloned();
    let target_addr = target_from_query;

    if let Some(addr) = target_addr {
        info!("Target address for /forward: {}", addr);
        ws.on_upgrade(move |socket| handle_socket(socket, addr))
    } else {
        warn!("Target address not specified for /forward. Rejecting connection.");
        StatusCode::BAD_REQUEST.into_response()
    }
}

// Handle WebSocket connection
async fn handle_socket(ws: WebSocket, target_addr: String) {
    info!(
        "WebSocket connection established, connecting to target: {}",
        target_addr
    );

    // Connect to target TCP service
    match TcpStream::connect(&target_addr).await {
        Ok(target_stream) => {
            info!("Connected to target: {}", target_addr);
            if let Err(e) = handle_connection(ws, target_stream).await {
                error!("Error handling connection: {}", e);
            }
        }
        Err(e) => {
            error!("Failed to connect to target {}: {}", target_addr, e);
            // WebSocket will be closed automatically when it goes out of scope
        }
    }
}

// Handle bidirectional data forwarding (simplified version)
async fn handle_connection(ws: WebSocket, target_stream: TcpStream) -> Result<()> {
    let (mut ws_sink, mut ws_stream) = ws.split();
    let (mut target_reader, mut target_writer) = tokio::io::split(target_stream);

    // WebSocket to target TCP
    let t1 = async move {
        while let Some(msg) = ws_stream.next().await {
            let msg = match msg {
                Ok(msg) => msg,
                Err(e) => {
                    error!("Error receiving WebSocket message: {}", e);
                    return Err(TunnelError::Other(format!(
                        "WebSocket receive error: {}",
                        e
                    )));
                }
            };

            match msg {
                Message::Binary(data) => {
                    if let Err(e) = target_writer.write_all(&data).await {
                        error!("Error writing to target: {}", e);
                        return Err(TunnelError::IoError(e));
                    }
                }
                Message::Text(data) => {
                    if let Err(e) = target_writer.write_all(data.as_bytes()).await {
                        error!("Error writing to target: {}", e);
                        return Err(TunnelError::IoError(e));
                    }
                }
                Message::Close(_) => {
                    info!("WebSocket closed by client");
                    return Ok(());
                }
                Message::Ping(_) => debug!("Received ping"),
                Message::Pong(_) => debug!("Received pong"),
            }
        }
        info!("WebSocket stream ended");
        Ok(())
    };

    // Target TCP to WebSocket
    let t2 = async move {
        let mut buffer = vec![0u8; DEFAULT_BUFFER_SIZE];
        loop {
            let n = match target_reader.read(&mut buffer).await {
                Ok(0) => {
                    debug!("Target TCP connection closed (EOF)");
                    // 优雅地关闭WebSocket连接
                    let _ = ws_sink.send(Message::Close(None)).await;
                    return Ok(());
                }
                Ok(n) => n,
                Err(e) => {
                    error!("Error reading from target: {}", e);
                    return Err(TunnelError::IoError(e));
                }
            };

            let data = &buffer[..n];
            // 修正: 使用 .to_vec() 并移除不必要的 `bytes` crate 依赖
            if let Err(e) = ws_sink
                .send(Message::Binary(Bytes::copy_from_slice(data)))
                .await
            {
                error!("Error sending data to WebSocket: {}", e);
                return Err(TunnelError::Other(format!("WebSocket send error: {}", e)));
            }
        }
    };

    // Run both tasks concurrently, terminate if either ends
    select! {
        result1 = t1 => result1,
        result2 = t2 => result2,
    }
}
