//! 处理/hub/forward WebSocket连接
//! 解析客户端发送的目标地址，分发target指令给对应的hub-service

use crate::common::{HubMessage, Result, TunnelError, ConnectionId};
use crate::server_hub::ServiceHub;
use crate::server_hub_dynamic::DynamicHubState;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade}, Extension,
        Query,
    },
    http::StatusCode,
    response::IntoResponse,
};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// 处理WebSocket连接请求for Hub转发功能
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    info!("处理新的WebSocket连接 /hub/forward路由");

    // 从查询参数获取service_id和target
    let service_id = params.get("service_id").cloned();
    let target_addr = params.get("target").cloned();

    if let (Some(service_id), Some(target_addr)) = (service_id, target_addr) {
        info!(
            "/hub/forward - 服务ID: {}, 目标: {}",
            service_id, target_addr
        );

        // 检查服务是否可用 - 先检查 dynamic hub state，再检查传统 hub state
        let is_available = {
            let dynamic_state = dynamic_hub_state.lock().await;
            if dynamic_state.is_service_available(&service_id) {
                true
            } else {
                let hub = service_hub.read().await;
                hub.is_service_available(&service_id)
            }
        };

        if !is_available {
            warn!("服务 {} 不可用", service_id);
            return StatusCode::SERVICE_UNAVAILABLE.into_response();
        }

        ws.on_upgrade(move |socket| {
            handle_hub_client_socket(
                socket,
                service_id,
                service_hub,
                dynamic_hub_state,
                HubClientType::Forward { target_addr },
            )
        })
    } else {
        warn!("缺少service_id或target参数");
        StatusCode::BAD_REQUEST.into_response()
    }
}

/// Hub客户端类型
#[derive(Debug, Clone)]
enum HubClientType {
    Forward { target_addr: String },
}

/// 统一的Hub客户端处理器
async fn handle_hub_client_socket(
    ws: WebSocket,
    service_id: String,
    service_hub: Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
    client_type: HubClientType,
) {
    let type_name = match &client_type {
        HubClientType::Forward { .. } => "转发",
    };

    info!("建立Hub{}WebSocket连接，服务ID: {}", type_name, service_id);

    // 生成连接ID
    let connection_id = {
        let mut hub = service_hub.write().await;
        hub.generate_connection_id()
    };

    // 根据类型处理初始化逻辑
    let ws = match client_type {
        HubClientType::Forward { target_addr } => {
            // 发送转发指令
            if let Err(e) = send_service_instruction(
                &service_hub,
                &dynamic_hub_state,
                &service_id,
                connection_id,
                "target",
                &target_addr,
            )
            .await
            {
                error!("发送转发指令失败: {}", e);
                return;
            }
            ws
        }
    };

    info!(
        "已发送{}指令给服务 {}, 连接ID: {}",
        type_name, service_id, connection_id
    );

    // 注册客户端连接
    let (ws_sink, ws_stream) = ws.split();
    let ws_sink = Arc::new(tokio::sync::Mutex::new(ws_sink));

    {
        let mut hub = service_hub.write().await;
        hub.register_client(
            connection_id.clone(),
            service_id.clone(),
            Arc::clone(&ws_sink),
        );
    }

    // 同时在 dynamic hub state 中注册客户端到服务的映射和客户端连接
    {
        let mut dynamic_state = dynamic_hub_state.lock().await;
        let client_id = format!("legacy_{}", connection_id);
        dynamic_state.register_client_to_service(client_id.clone(), service_id.clone());
        dynamic_state.register_client_connection(client_id, Arc::clone(&ws_sink));
    }

    info!("已注册客户端连接，连接ID: {}", connection_id);

    // 启动消息处理
    if let Err(e) =
        handle_client_messages(ws_stream, connection_id, service_hub.clone(), dynamic_hub_state.clone()).await
    {
        error!("处理客户端消息失败: {}", e);
    }

    // 清理连接
    {
        let mut hub = service_hub.write().await;
        hub.handle_client_disconnect(connection_id).await;
    }

    // 同时清理 dynamic hub state 中的映射
    {
        let mut dynamic_state = dynamic_hub_state.lock().await;
        let client_id = format!("legacy_{}", connection_id);
        dynamic_state.cleanup_client(&client_id);
    }

    info!("Hub{}连接已断开，连接ID: {}", type_name, connection_id);
}

/// 发送服务指令给hub-service (共享函数)
async fn send_service_instruction(
    service_hub: &Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: &Arc<tokio::sync::Mutex<DynamicHubState>>,
    service_id: &str,
    connection_id: ConnectionId,
    instruction_type: &str,
    target_addr: &str,
) -> Result<()> {
    // 先尝试从 dynamic hub state 获取连接
    let dynamic_hub_service_sink = {
        let dynamic_state = dynamic_hub_state.lock().await;
        dynamic_state.get_control_channel(service_id)
    };

    if let Some(sink) = dynamic_hub_service_sink {
        // 对于 dynamic hub state，我们发送 HubMessage::ServiceForwardInstruction
        let instruction = HubMessage::ServiceForwardInstruction {
            connection_id,
            instruction_type: instruction_type.to_string(),
            target_addr: target_addr.to_string(),
        };

        let json = serde_json::to_string(&instruction).map_err(|e| {
            error!("序列化指令失败: {}", e);
            TunnelError::Other(format!("JSON序列化错误: {}", e))
        })?;

        sink.lock()
            .await
            .send(Message::Text(json.into()))
            .await
            .map_err(|e| {
                error!("发送指令失败: {}", e);
                TunnelError::Other(format!("WebSocket发送错误: {}", e))
            })?;

        info!(
            "已发送{}指令给 dynamic hub service {}，连接ID: {}",
            instruction_type, service_id, connection_id
        );
        return Ok(());
    }

    // 如果 dynamic hub state 中没有，尝试从传统的 ServiceHub 获取
    let hub_service_sink = {
        let hub = service_hub.read().await;
        hub.get_hubservice_sink(service_id)
    };

    if let Some(sink) = hub_service_sink {
        let instruction = HubMessage::ServiceForwardInstruction {
            connection_id,
            instruction_type: instruction_type.to_string(),
            target_addr: target_addr.to_string(),
        };

        let json = serde_json::to_string(&instruction).map_err(|e| {
            error!("序列化指令失败: {}", e);
            TunnelError::Other(format!("JSON序列化错误: {}", e))
        })?;

        sink.lock()
            .await
            .send(Message::Text(json.into()))
            .await
            .map_err(|e| {
                error!("发送指令失败: {}", e);
                TunnelError::Other(format!("WebSocket发送错误: {}", e))
            })?;

        info!(
            "已发送{}指令给传统 hub service {}，连接ID: {}",
            instruction_type, service_id, connection_id
        );
        Ok(())
    } else {
        error!("未找到服务: {}", service_id);
        Err(TunnelError::ServiceUnavailable(format!(
            "Service '{}' is not available",
            service_id
        )))
    }
}

/// 处理客户端WebSocket消息（共享函数）
async fn handle_client_messages(
    mut ws_stream: futures_util::stream::SplitStream<WebSocket>,
    connection_id: ConnectionId,
    service_hub: Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
) -> Result<()> {
    // 等待数据隧道建立
    let client_id = format!("legacy_{}", connection_id);
    
    // 给数据隧道一些时间来建立连接
    for attempt in 1..=50 { // 最多等待5秒（50 * 100ms）
        {
            let dynamic_state = dynamic_hub_state.lock().await;
            if dynamic_state.is_tunnel_active(&client_id) {
                debug!("数据隧道已建立，客户端ID: {}", client_id);
                break;
            }
        }
        
        if attempt == 50 {
            warn!("数据隧道建立超时，客户端ID: {}", client_id);
            return Err(TunnelError::Other("数据隧道建立超时".to_string()));
        }
        
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }
    
    while let Some(message) = ws_stream.next().await {
        let message = match message {
            Ok(msg) => msg,
            Err(e) => {
                error!("接收WebSocket消息时出错: {}", e);
                return Err(TunnelError::Other(format!("WebSocket接收错误: {}", e)));
            }
        };

        match message {
            Message::Binary(data) => {
                debug!(
                    "收到来自客户端 {} 的 {} 字节二进制数据",
                    connection_id,
                    data.len()
                );

                // 优先通过 dynamic hub state 转发数据
                let dynamic_forwarded = {
                    let dynamic_state = dynamic_hub_state.lock().await;
                    dynamic_state.forward_client_to_tunnel(client_id.clone(), data.to_vec()).await
                };

                if let Err(e) = dynamic_forwarded {
                    debug!("Dynamic hub转发失败: {}, 尝试传统转发", e);
                    // 如果 dynamic hub state 转发失败，尝试传统的转发方式
                    let hub = service_hub.read().await;
                    if let Err(e) = hub
                        .forward_client_to_hubservice(connection_id, data.to_vec())
                        .await
                    {
                        error!("转发数据失败: {}", e);
                        return Err(e);
                    }
                } else {
                    debug!("成功通过 dynamic hub state 转发数据");
                }
            }
            Message::Text(text) => {
                debug!("收到来自客户端 {} 的文本消息: {}", connection_id, text);
                // 目前不需要特殊的文本消息处理
            }
            Message::Close(_) => {
                info!("客户端 {} 关闭了WebSocket连接", connection_id);
                break;
            }
            Message::Ping(_) => debug!("收到ping"),
            Message::Pong(_) => debug!("收到pong"),
        }
    }

    info!("客户端消息处理循环结束，连接ID: {}", connection_id);
    Ok(())
}
