//! 服务端动态隧道支持模块
//! 
//! 这个模块实现服务端对动态隧道模式的支持，包括：
//! - 控制通道管理 (/hub/dynamic)
//! - 数据隧道协调 (/hub/data) 
//! - 客户端连接管理

use crate::common::{
    ControlMessage, DataTunnelMessage, Result, TunnelError, HubMessage,
    deserialize_control_message, deserialize_data_tunnel_message,
    serialize_data_tunnel_message,
    connect_websocket_with_retry, prepare_ws_url,
};
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
    },
    response::IntoResponse,
};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use bytes::Bytes;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio_tungstenite::tungstenite::Message as TungsteniteMessage;


/// 动态隧道服务端状态管理
#[derive(Default, Debug)]
pub struct DynamicHubState {
    /// 注册的Hub控制通道：hub_id -> WebSocket发送端
    control_channels: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
    /// 等待数据隧道连接的客户端：client_id -> (客户端WebSocket发送端, 客户端WebSocket接收端)
    pending_clients: HashMap<String, (Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>, futures_util::stream::SplitStream<WebSocket>)>,
    /// 活跃的数据隧道：client_id -> 数据隧道WebSocket发送端
    active_tunnels: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
    /// 客户端连接ID到服务ID的映射 (用于传统forward请求)
    client_to_service: HashMap<String, String>,
    /// 客户端连接：client_id -> 客户端WebSocket发送端 (用于传统forward请求的响应数据转发)
    client_connections: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
}

impl DynamicHubState {
    pub fn new() -> Self {
        Self::default()
    }

    /// 检查服务是否可用
    pub fn is_service_available(&self, service_id: &str) -> bool {
        self.control_channels.contains_key(service_id)
    }

    /// 获取可用的服务列表
    pub fn get_available_services(&self) -> Vec<String> {
        self.control_channels.keys().cloned().collect()
    }

    /// 获取控制通道连接
    pub fn get_control_channel(&self, service_id: &str) -> Option<Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>> {
        self.control_channels.get(service_id).cloned()
    }

    /// 注册客户端到服务的映射 (用于传统forward请求)
    pub fn register_client_to_service(&mut self, client_id: String, service_id: String) {
        self.client_to_service.insert(client_id, service_id);
    }

    /// 注册客户端连接 (用于传统forward请求的响应数据转发)
    pub fn register_client_connection(&mut self, client_id: String, client_sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>) {
        self.client_connections.insert(client_id, client_sink);
    }

    /// 注册数据隧道
    pub fn register_data_tunnel(&mut self, client_id: String, tunnel_sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>) {
        self.active_tunnels.insert(client_id, tunnel_sink);
    }

    /// 转发数据到对应的数据隧道
    pub async fn forward_client_to_tunnel(&self, client_id: String, data: Vec<u8>) -> Result<()> {
        if let Some(tunnel_sink) = self.active_tunnels.get(&client_id) {
            debug!("转发 {} 字节数据到数据隧道，客户端: {}", data.len(), client_id);
            
            tunnel_sink
                .lock()
                .await
                .send(Message::Binary(Bytes::from(data)))
                .await
                .map_err(|e| {
                    error!("转发数据到数据隧道失败: {}", e);
                    TunnelError::Other(format!("WebSocket发送错误: {}", e))
                })?;
            
            Ok(())
        } else {
            error!("未找到客户端 {} 的数据隧道", client_id);
            Err(TunnelError::ServiceUnavailable(format!(
                "Data tunnel for client '{}' not found",
                client_id
            )))
        }
    }

    /// 转发数据到对应的客户端连接 (用于传统forward请求的响应数据转发)
    pub async fn forward_tunnel_to_client(&self, client_id: String, data: Vec<u8>) -> Result<()> {
        if let Some(client_sink) = self.client_connections.get(&client_id) {
            debug!("转发 {} 字节响应数据到客户端: {}", data.len(), client_id);
            
            client_sink
                .lock()
                .await
                .send(Message::Binary(Bytes::from(data)))
                .await
                .map_err(|e| {
                    error!("转发数据到客户端失败: {}", e);
                    TunnelError::Other(format!("WebSocket发送错误: {}", e))
                })?;
            
            Ok(())
        } else {
            error!("未找到客户端 {} 的连接", client_id);
            Err(TunnelError::ServiceUnavailable(format!(
                "Client connection '{}' not found",
                client_id
            )))
        }
    }

    /// 清理客户端连接
    pub fn cleanup_client(&mut self, client_id: &str) {
        self.client_to_service.remove(client_id);
        self.active_tunnels.remove(client_id);
        self.pending_clients.remove(client_id);
        self.client_connections.remove(client_id);
    }
    
    /// 检查数据隧道是否已建立
    pub fn is_tunnel_active(&self, client_id: &str) -> bool {
        self.active_tunnels.contains_key(client_id)
    }
}

/// 控制通道WebSocket处理器 (/hub/dynamic)
pub async fn dynamic_control_handler(
    ws: WebSocketUpgrade,
    Extension(state): Extension<Arc<Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_control_channel(socket, state))
}

/// 数据隧道WebSocket处理器 (/hub/data)
pub async fn dynamic_data_handler(
    ws: WebSocketUpgrade,
    Extension(state): Extension<Arc<Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_data_tunnel(socket, state))
}

/// 处理控制通道连接
async fn handle_control_channel(socket: WebSocket, state: Arc<Mutex<DynamicHubState>>) {
    info!("新的控制通道连接建立");
    
    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));
    
    // 等待注册消息
    if let Some(Ok(Message::Text(text))) = stream.next().await {
        match deserialize_control_message(&text) {
            Ok(ControlMessage::RegisterHub { hub_id }) => {
                info!("Hub '{}' 控制通道已注册", hub_id);
                
                // 注册控制通道
                {
                    let mut state_guard = state.lock().await;
                    state_guard.control_channels.insert(hub_id.clone(), sink.clone());
                }
                
                // 监听来自Hub的其他控制消息
                while let Some(message) = stream.next().await {
                    match message {
                        Ok(Message::Text(text)) => {
                            if let Err(e) = handle_hub_control_message(&text, &state, &hub_id).await {
                                error!("处理Hub控制消息失败: {}", e);
                            }
                        }
                        Ok(Message::Close(_)) => {
                            info!("Hub '{}' 控制通道关闭", hub_id);
                            break;
                        }
                        Ok(_) => {
                            // 忽略其他消息类型
                        }
                        Err(e) => {
                            error!("控制通道接收消息错误: {}", e);
                            break;
                        }
                    }
                }
                
                // 清理注册的控制通道
                {
                    let mut state_guard = state.lock().await;
                    state_guard.control_channels.remove(&hub_id);
                }
                info!("Hub '{}' 控制通道已断开", hub_id);
            }
            _ => {
                warn!("控制通道接收到无效的注册消息");
            }
        }
    } else {
        warn!("控制通道未收到有效的注册消息");
    }
}

/// 处理数据隧道连接
async fn handle_data_tunnel(socket: WebSocket, state: Arc<Mutex<DynamicHubState>>) {
    info!("新的数据隧道连接建立");
    
    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));
    
    // 等待数据隧道注册消息
    if let Some(Ok(Message::Text(text))) = stream.next().await {
        match deserialize_data_tunnel_message(&text) {
            Ok(DataTunnelMessage::RegisterDataTunnel { client_id }) => {
                info!("数据隧道为客户端 '{}' 注册", client_id);
                
                // 先检查是否是等待的客户端连接 (真正的 dynamic tunnel 客户端)
                let client_connection = {
                    let mut state_guard = state.lock().await;
                    state_guard.pending_clients.remove(&client_id)
                };
                
                if let Some((client_sink, mut client_stream)) = client_connection {
                    info!("找到等待的客户端 '{}'，开始数据转发", client_id);
                    
                    // 启动双向数据转发 (真正的 dynamic tunnel 客户端)
                    let upload_task = {
                        let mut client_sink = client_sink.lock_owned().await;
                        tokio::spawn(async move {
                            while let Some(message) = stream.next().await {
                                match message {
                                    Ok(msg) => {
                                        if client_sink.send(msg).await.is_err() {
                                            debug!("客户端连接已关闭，停止上行转发");
                                            break;
                                        }
                                    }
                                    Err(e) => {
                                        error!("数据隧道接收错误: {}", e);
                                        break;
                                    }
                                }
                            }
                        })
                    };
                    
                    let download_task = tokio::spawn(async move {
                        while let Some(message) = client_stream.next().await {
                            match message {
                                Ok(msg) => {
                                    if sink.lock().await.send(msg).await.is_err() {
                                        debug!("数据隧道连接已关闭，停止下行转发");
                                        break;
                                    }
                                }
                                Err(e) => {
                                    error!("客户端接收错误: {}", e);
                                    break;
                                }
                            }
                        }
                    });
                    
                    // 等待任意一个方向的传输结束
                    tokio::select! {
                        _ = upload_task => debug!("客户端 '{}' 上行传输结束", client_id),
                        _ = download_task => debug!("客户端 '{}' 下行传输结束", client_id),
                    }
                } else {
                    // 这是传统的 forward 请求产生的数据隧道
                    info!("注册数据隧道，客户端ID: {}", client_id);
                    
                    // 将数据隧道注册到 active_tunnels 中
                    {
                        let mut state_guard = state.lock().await;
                        state_guard.register_data_tunnel(client_id.clone(), sink.clone());
                    }
                    
                    // 监听从hub-service发送来的响应数据
                    // 这些数据需要转发给对应的客户端连接
                    // 对于传统的 forward 请求，数据转发是双向的：
                    // 客户端 -> /hub/forward -> 数据隧道 -> hub-service -> 目标
                    // 响应数据：目标 -> hub-service -> 数据隧道 -> /hub/forward -> 客户端
                    
                    while let Some(message) = stream.next().await {
                        match message {
                            Ok(Message::Close(_)) => {
                                info!("数据隧道 '{}' 关闭", client_id);
                                break;
                            }
                            Ok(Message::Binary(data)) => {
                                debug!("数据隧道 '{}' 收到来自hub-service的 {} 字节数据", client_id, data.len());
                                // 将数据转发给对应的客户端连接
                                let state_guard = state.lock().await;
                                if let Err(e) = state_guard.forward_tunnel_to_client(client_id.clone(), data.to_vec()).await {
                                    error!("转发响应数据到客户端失败: {}", e);
                                    break;
                                }
                            }
                            Ok(_) => {
                                // 忽略其他消息类型
                            }
                            Err(e) => {
                                error!("数据隧道接收错误: {}", e);
                                break;
                            }
                        }
                    }
                    
                    // 清理数据隧道注册
                    {
                        let mut state_guard = state.lock().await;
                        state_guard.cleanup_client(&client_id);
                    }
                }
            }
            Err(e) => {
                error!("无法解析数据隧道注册消息: {}", e);
            }
        }
    } else {
        warn!("数据隧道注册失败：未找到等待的客户端 '{}'", "unknown");
    }
}

/// 处理来自Hub的控制消息
async fn handle_hub_control_message(
    text: &str,
    state: &Arc<Mutex<DynamicHubState>>,
    hub_id: &str,
) -> Result<()> {
    // 首先尝试解析为 ControlMessage (动态隧道专用消息)
    if let Ok(control_msg) = deserialize_control_message(text) {
        debug!("收到控制消息: {:?}", control_msg);
        
        match control_msg {
            ControlMessage::PrepareNewTunnel { client_id: _ } => {
                warn!("收到PrepareNewTunnel消息，但当前不支持此功能");
            }
            ControlMessage::TunnelReady { tunnel_id, client_id } => {
                debug!("收到隧道就绪消息: tunnel_id={}, client_id={}", tunnel_id, client_id);
            }
            ControlMessage::TunnelCreationFailed { client_id, reason } => {
                error!("Hub '{}' 报告客户端 '{}' 隧道创建失败: {}", hub_id, client_id, reason);
            }
            ControlMessage::RegisterHub { .. } => {
                debug!("收到注册Hub消息回显");
            }
        }
        
        return Ok(());
    }

    // 如果不是 ControlMessage，尝试解析为 HubMessage (统一消息格式)
    if let Ok(hub_msg) = serde_json::from_str::<HubMessage>(text) {
        debug!("收到Hub消息: {:?}", hub_msg);
        
        match hub_msg {
            HubMessage::HubServiceHeartbeat { service_id, timestamp } => {
                debug!("收到心跳消息: service_id={}, timestamp={}", service_id, timestamp);
                // 心跳消息不需要特殊处理，只是确认连接活跃
            }
            HubMessage::ServiceForwardInstruction { connection_id, instruction_type, target_addr } => {
                info!("收到转发指令: connection_id={}, type={}, target={}", 
                    connection_id, instruction_type, target_addr);
                
                // 处理转发指令
                if instruction_type == "target" {
                    // 为这个连接创建一个隧道
                    let client_id = format!("legacy_{}", connection_id);
                    
                    // 创建隧道任务
                    let tunnel_task = create_tunnel_task(
                        client_id.clone(),
                        target_addr,
                        state.clone(),
                    );
                    
                    tokio::spawn(tunnel_task);
                }
            }
            _ => {
                warn!("收到不支持的Hub消息类型");
            }
        }
        
        return Ok(());
    }

    // 如果都不是，记录错误
    warn!("收到无法解析的控制消息: {}", text);
    Ok(())
}

/// 创建隧道任务
async fn create_tunnel_task(
    client_id: String,
    target_addr: String,
    state: Arc<Mutex<DynamicHubState>>,
) -> Result<()> {
    info!("创建隧道任务，客户端ID: {}, 目标: {}", client_id, target_addr);
    
    // 连接到目标服务器
    let target_stream = match TcpStream::connect(&target_addr).await {
        Ok(stream) => stream,
        Err(e) => {
            error!("无法连接到目标 {}: {}", target_addr, e);
            
            // 发送隧道创建失败消息
            let failure_msg = ControlMessage::TunnelCreationFailed {
                client_id: client_id.clone(),
                reason: format!("无法连接到本地目标: {}", e),
            };
            
            // 这里应该发送给Hub，但为了简化，我们先记录错误
            debug!("应该发送隧道创建失败消息: {:?}", failure_msg);
            
            return Err(TunnelError::Other(format!("Failed to connect to target: {}", e)));
        }
    };

    // 连接到数据隧道端点
    let server_url = "ws://127.0.0.1:8060"; // 这应该从配置中获取
    let clean_server_url = crate::common::trim_server_url_slashes(server_url);
    let data_ws_url = prepare_ws_url(&format!("{}/hub/data", clean_server_url))?;
    
    let data_ws_stream = match connect_websocket_with_retry(&data_ws_url, 3, 1000, None).await {
        Ok(stream) => stream,
        Err(e) => {
            error!("无法连接到数据隧道端点: {}", e);
            return Err(e);
        }
    };
    
    let (mut data_ws_sink, mut data_ws_stream) = data_ws_stream.split();
    
    // 注册数据隧道
    let register_msg = DataTunnelMessage::RegisterDataTunnel {
        client_id: client_id.clone(),
    };
    
    let json_str = serialize_data_tunnel_message(&register_msg)?;
    data_ws_sink.send(TungsteniteMessage::Text(json_str)).await
        .map_err(|e| TunnelError::Other(format!("发送数据隧道注册消息失败: {}", e)))?;
    
    info!("数据隧道注册成功，客户端ID: {}", client_id);
    
    // 启动双向数据转发
    let (mut target_read, mut target_write) = target_stream.into_split();
    
    let upload_task = tokio::spawn(async move {
        while let Some(message) = data_ws_stream.next().await {
            match message {
                Ok(TungsteniteMessage::Binary(data)) => {
                    if let Err(e) = target_write.write_all(&data).await {
                        error!("写入目标服务器失败: {}", e);
                        break;
                    }
                }
                Ok(TungsteniteMessage::Close(_)) => {
                    debug!("数据隧道关闭");
                    break;
                }
                Ok(_) => {} // 忽略其他消息
                Err(e) => {
                    error!("数据隧道接收错误: {}", e);
                    break;
                }
            }
        }
    });
    
    let download_task = tokio::spawn(async move {
        loop {
            let mut buffer = vec![0u8; 32768];
            match target_read.read(&mut buffer).await {
                Ok(0) => {
                    debug!("目标连接关闭");
                    break;
                }
                Ok(n) => {
                    let data = &buffer[..n];
                    if let Err(e) = data_ws_sink.send(TungsteniteMessage::Binary(data.to_vec())).await {
                        error!("发送数据到数据隧道失败: {}", e);
                        break;
                    }
                }
                Err(e) => {
                    error!("从目标读取数据失败: {}", e);
                    break;
                }
            }
        }
    });
    
    // 等待任意一个方向的传输结束
    tokio::select! {
        _ = upload_task => debug!("客户端 '{}' 上行传输结束", client_id),
        _ = download_task => debug!("客户端 '{}' 下行传输结束", client_id),
    }
    
    // 清理状态
    {
        let mut state_guard = state.lock().await;
        state_guard.cleanup_client(&client_id);
    }
    
    Ok(())
}

 