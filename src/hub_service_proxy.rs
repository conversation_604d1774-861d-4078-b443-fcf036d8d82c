//! 处理/hub/proxy WebSocket连接
//! 解析客户端发送的代理协议，分发proxy指令给对应的hub-service

use crate::common::{
    HubMessage, Result, TunnelError, ConnectionId,
};
use crate::proxy_server::parse_proxy_request;
use crate::server_hub::ServiceHub;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
        Query,
    },
    http::StatusCode,
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// 处理WebSocket连接请求for Hub代理功能
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
) -> impl IntoResponse {
    info!("处理新的WebSocket连接 /hub/proxy路由");

    // 从查询参数获取service_id
    let service_id = params.get("service_id").cloned();

    if let Some(service_id) = service_id {
        info!("/hub/proxy - 服务ID: {}", service_id);

        // 检查服务是否可用
        let is_available = {
            let hub = service_hub.read().await;
            hub.is_service_available(&service_id)
        };

        if !is_available {
            warn!("服务 {} 不可用", service_id);
            return StatusCode::SERVICE_UNAVAILABLE.into_response();
        }

        ws.on_upgrade(move |socket| handle_hub_proxy_socket(socket, service_id.to_string(), service_hub))
    } else {
        warn!("缺少service_id参数");
        StatusCode::BAD_REQUEST.into_response()
    }
}

// 处理Hub代理WebSocket连接
async fn handle_hub_proxy_socket(
    ws: WebSocket,
    service_id: String,
    service_hub: Arc<RwLock<ServiceHub>>,
) {
    info!("建立Hub代理WebSocket连接，服务ID: {}", service_id);

    // 生成连接ID
    let connection_id = {
        let mut hub = service_hub.write().await;
        hub.generate_connection_id()
    };

    // 分离WebSocket流
    let (mut ws_sink, mut ws_stream) = ws.split();

    // 等待第一个消息，解析代理协议
    let proxy_request = match ws_stream.next().await {
        Some(Ok(Message::Binary(data))) => {
            // 解析代理协议
            let mut cursor = std::io::Cursor::new(data.clone());
            match parse_proxy_request(&mut cursor).await {
                Ok((proxy_request, response_data)) => {
                    info!(
                        "解析到代理请求: {:?}://{}:{}",
                        proxy_request.protocol,
                        proxy_request.target_host,
                        proxy_request.target_port
                    );

                    // 如果有响应数据，先发送回客户端
                    if let Some(response_bytes) = response_data {
                        if let Err(e) = ws_sink
                            .send(Message::Binary(Bytes::from(response_bytes)))
                            .await
                        {
                            error!("发送代理响应失败: {}", e);
                            return;
                        }
                    }

                    proxy_request
                }
                Err(e) => {
                    error!("解析代理请求失败: {}", e);
                    return;
                }
            }
        }
        Some(Ok(msg)) => {
            error!("期望二进制消息，收到: {:?}", msg);
            return;
        }
        Some(Err(e)) => {
            error!("接收WebSocket消息时出错: {}", e);
            return;
        }
        None => {
            warn!("WebSocket连接立即关闭");
            return;
        }
    };

    // 发送代理指令给hub-service（包含目标地址）
    let target_addr = format!(
        "{}:{}",
        proxy_request.target_host, proxy_request.target_port
    );

    if let Err(e) = send_service_instruction(
        &service_hub,
        &service_id,
        connection_id,
        "proxy",
        &target_addr,
    )
    .await
    {
        error!("发送代理指令失败: {}", e);
        return;
    }

    info!(
        "已发送代理指令给服务 {}, 连接ID: {}, 目标: {}",
        service_id, connection_id, target_addr
    );

    // 重新合并WebSocket
    let ws = ws_sink.reunite(ws_stream).expect("重新合并WebSocket失败");
    let (ws_sink, ws_stream) = ws.split();
    let ws_sink = Arc::new(tokio::sync::Mutex::new(ws_sink));

    // 注册客户端连接
    {
        let mut hub = service_hub.write().await;
        hub.register_client(
            connection_id,
            service_id.clone(),
            Arc::clone(&ws_sink),
        );
    }

    info!("已注册客户端连接，连接ID: {}", connection_id);

    // 启动消息处理
    if let Err(e) =
        handle_client_messages(ws_stream, connection_id, service_hub.clone()).await
    {
        error!("处理客户端消息失败: {}", e);
    }

    // 清理连接
    {
        let mut hub = service_hub.write().await;
        hub.handle_client_disconnect(connection_id).await;
    }

    info!("Hub代理连接已断开，连接ID: {}", connection_id);
}

/// 发送服务指令给hub-service (共享函数)
async fn send_service_instruction(
    service_hub: &Arc<RwLock<ServiceHub>>,
    service_id: &str,
    connection_id: ConnectionId,
    instruction_type: &str,
    target_addr: &str,
) -> Result<()> {
    let instruction = HubMessage::ServiceForwardInstruction {
        connection_id,
        instruction_type: instruction_type.to_string(),
        target_addr: target_addr.to_string(),
    };

    let instruction_json = serde_json::to_string(&instruction)
        .map_err(|e| TunnelError::Other(format!("序列化指令失败: {}", e)))?;

    // 获取服务提供者的sink并发送指令
    let hub = service_hub.read().await;
    if let Some(hubservice_sink) = hub.get_hubservice_sink(service_id) {
        let mut sink = hubservice_sink.lock().await;
        sink.send(Message::Text(instruction_json.into()))
            .await
            .map_err(|e| TunnelError::Other(format!("发送指令失败: {}", e)))?;
    } else {
        return Err(TunnelError::Other(format!(
            "未找到服务 {} 的提供者",
            service_id
        )));
    }

    Ok(())
}

/// 处理客户端WebSocket消息（共享函数）
async fn handle_client_messages(
    mut ws_stream: futures_util::stream::SplitStream<WebSocket>,
    connection_id: ConnectionId,
    service_hub: Arc<RwLock<ServiceHub>>,
) -> Result<()> {
    while let Some(message) = ws_stream.next().await {
        let message = match message {
            Ok(msg) => msg,
            Err(e) => {
                error!("接收WebSocket消息时出错: {}", e);
                return Err(TunnelError::Other(format!("WebSocket接收错误: {}", e)));
            }
        };

        match message {
            Message::Binary(data) => {
                debug!(
                    "收到来自客户端 {} 的 {} 字节二进制数据",
                    connection_id,
                    data.len()
                );

                // 转发到服务提供者
                let hub = service_hub.read().await;
                if let Err(e) = hub
                    .forward_client_to_hubservice(connection_id, data.to_vec())
                    .await
                {
                    error!("转发数据失败: {}", e);
                    return Err(e);
                }
            }
            Message::Text(text) => {
                debug!("收到来自客户端 {} 的文本消息: {}", connection_id, text);
                // 目前不需要特殊的文本消息处理
            }
            Message::Close(_) => {
                info!("客户端 {} 关闭了WebSocket连接", connection_id);
                break;
            }
            Message::Ping(_) => debug!("收到ping"),
            Message::Pong(_) => debug!("收到pong"),
        }
    }

    info!("客户端消息处理循环结束，连接ID: {}", connection_id);
    Ok(())
}
