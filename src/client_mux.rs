use crate::common::{
    add_connection_id_prefix, deserialize_client_mux_message, extract_connection_id_and_payload,
    generate_origin_header, prepare_ws_url, serialize_client_mux_message, ClientMuxMessage,
    ConnectionId, ConnectionIdGenerator, Result, TunnelError, DEFAULT_BUFFER_SIZE,
};
use base64::{engine::general_purpose::STANDARD, Engine as _};
use futures_util::{
    sink::SinkExt,
    stream::StreamExt,
};
use log::{debug, error, info, warn};
use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicUsize, Ordering},
        Arc,
    },
    time::Duration,
};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::{TcpListener, TcpStream},
    sync::{mpsc, Mutex, RwLock},
};
use tokio_tungstenite::{
    connect_async,
    tungstenite::{client::IntoClientRequest, Message},
    MaybeTlsStream, WebSocketStream,
};


/// 客户端 Mux 配置
#[derive(Debug, Clone)]
pub struct ClientMuxConfig {
    pub listen_addr: String,     // 本地监听地址
    pub server_url: String,      // WebSocket 服务器 URL
    pub target_addr: Option<String>, // 目标地址（Forward 模式）
    pub username: Option<String>, // 认证用户名
    pub password: Option<String>, // 认证密码
    pub mux_pool_size: usize,    // Mux 连接池大小
}

impl Default for ClientMuxConfig {
    fn default() -> Self {
        Self {
            listen_addr: "127.0.0.1:8080".to_string(),
            server_url: "wss://localhost:8060".to_string(),
            target_addr: None,
            username: None,
            password: None,
            mux_pool_size: 10,
        }
    }
}


/// Client Mux manager
pub struct ClientMuxManager {
    config: ClientMuxConfig,
    id_generator: ConnectionIdGenerator,
    stats: Arc<RwLock<MuxStats>>,

    // --- 新的连接池和会话管理状态 ---
    session_id: Arc<RwLock<Option<u32>>>,
    // pool 字段被移除
    // live_connections 字段被移除
    
    /// 手动管理的写操作池，为每个连接的写任务提供一个发送端
    ws_sinks: Arc<RwLock<Vec<Option<mpsc::UnboundedSender<Message>>>>>,

    /// 用于轮询分发的原子计数器
    round_robin_counter: Arc<AtomicUsize>,

    /// 存储 ConnectionId 到 live_connections 索引的映射，用于上行数据路由
    stream_to_ws_map: Arc<Mutex<HashMap<ConnectionId, usize>>>,

    /// 存储 ConnectionId 到其下行数据通道发送端的映射
    active_streams: Arc<Mutex<HashMap<ConnectionId, mpsc::UnboundedSender<Vec<u8>>>>>,

    /// 存储等待连接响应的通道
    pending_responses: Arc<Mutex<HashMap<ConnectionId, mpsc::UnboundedSender<bool>>>>,
}

impl Clone for ClientMuxManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            id_generator: self.id_generator.clone(),
            stats: self.stats.clone(),
            session_id: self.session_id.clone(),
            ws_sinks: self.ws_sinks.clone(),
            round_robin_counter: self.round_robin_counter.clone(),
            stream_to_ws_map: self.stream_to_ws_map.clone(),
            active_streams: self.active_streams.clone(),
            pending_responses: self.pending_responses.clone(),
        }
    }
}

/// Mux 统计信息
#[derive(Debug, Clone)]
pub struct MuxStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

impl Default for MuxStats {
    fn default() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
        }
    }
}

impl ClientMuxManager {
    /// Create new client Mux manager
    pub fn new(config: ClientMuxConfig) -> Arc<Self> {
        // manager 和 pool 的创建逻辑被移除
        
        // 初始化我们的手动池，大小固定，初始为空
        let mut sinks = Vec::with_capacity(config.mux_pool_size);
        for _ in 0..config.mux_pool_size {
            sinks.push(None);
        }

        Arc::new(Self {
            config,
            id_generator: ConnectionIdGenerator::new(),
            stats: Arc::new(RwLock::new(MuxStats::default())),
            session_id: Arc::new(RwLock::new(None)),
            ws_sinks: Arc::new(RwLock::new(sinks)),
            round_robin_counter: Arc::new(AtomicUsize::new(0)),
            stream_to_ws_map: Arc::new(Mutex::new(HashMap::new())),
            active_streams: Arc::new(Mutex::new(HashMap::new())),
            pending_responses: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// Run client Mux manager
    pub async fn run(self: &Arc<Self>) -> Result<()> {
        // Start local server
        let listen_addr = self.config.listen_addr.clone();
        let manager_arc = Arc::clone(self);
        let local_server_task = tokio::spawn(start_local_server(listen_addr, manager_arc));

        // Start guardian tasks for all connections
        for i in 0..self.config.mux_pool_size {
            let manager_clone = Arc::clone(self);
            tokio::spawn(async move {
                manager_clone.connection_guardian_task(i).await;
            });
        }

        // Wait for local server task to complete (theoretically it should run forever)
        local_server_task.await.map_err(|e| TunnelError::Other(format!("Local server task failed: {}", e)))?
    }

    /// 单个WebSocket连接的守护任务，负责连接、读消息和重连
    async fn connection_guardian_task(self: &Arc<Self>, ws_index: usize) {
        loop {
            info!("Guardian task #{}: Establishing WebSocket connection...", ws_index);
            
            // 尝试建立连接并进行初始握手
            let ws_stream = match self.establish_and_handshake().await {
                Ok(stream) => {
                    info!("Guardian task #{}: WebSocket connection established and handshake completed.", ws_index);
                    stream
                }
                Err(e) => {
                    error!("Guardian task #{}: Failed to establish connection: {}. Will retry in 5 seconds...", ws_index, e);
                    // 清理可能存在的旧sink
                    self.ws_sinks.write().await[ws_index] = None;
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    continue;
                }
            };
            
            // 拆分流
            let (ws_sink, mut ws_stream) = ws_stream.split();

            // 创建一个向上游发送数据的通道
            let (upstream_tx, mut upstream_rx) = mpsc::unbounded_channel::<Message>();

            // 将新的发送端放入池中
            self.ws_sinks.write().await[ws_index] = Some(upstream_tx);

            // 启动专门的写任务
            let mut writer_task = tokio::spawn(async move {
                let mut ws_sink = ws_sink;
                while let Some(message) = upstream_rx.recv().await {
                    if ws_sink.send(message).await.is_err() {
                        break; // 发送失败，退出写任务
                    }
                }
            });

            // 在当前任务中处理读操作
            loop {
                tokio::select! {
                    // 监听下行消息
                    Some(message_result) = ws_stream.next() => {
                        match message_result {
                            Ok(message) => {
                                // 处理收到的消息
                                if self.handle_downstream_message(message).await.is_err() {
                                    // 处理失败，可能意味着逻辑错误，但我们继续监听
                                }
                            }
                            Err(_) => {
                                // 读错误意味着连接已死
                                break;
                            }
                        }
                    }
                    // 监听写任务是否已退出
                    _ = &mut writer_task => {
                        // 写任务退出，也意味着连接已死
                        break;
                    }
                }
            }

            // 如果循环退出，说明连接已断开
            warn!("Guardian task #{}: WebSocket connection disconnected. Will start reconnection immediately.", ws_index);
            // 清理sink，以便其他任务知道该连接不可用
            self.ws_sinks.write().await[ws_index] = None;
        }
    }

    /// 建立一个新的WebSocket连接并完成初始化握手
    async fn establish_and_handshake(&self) -> Result<WebSocketStream<MaybeTlsStream<TcpStream>>> {
        let mut url = prepare_ws_url(&self.config.server_url)?;
        url.set_path("/mux");
        let origin = generate_origin_header(&url);
        let mut request = url.into_client_request()?;
        request.headers_mut().insert("Origin", tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(&origin).map_err(|e| TunnelError::Other(format!("Invalid origin header: {}", e)))?);

        // 添加认证（如果需要）
        if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
            let auth = format!("{}:{}", username, password);
            let encoded = STANDARD.encode(auth.as_bytes());
            let auth_header = format!("Basic {}", encoded);
            request.headers_mut().insert("Authorization", tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(&auth_header).map_err(|e| TunnelError::Other(format!("Invalid auth header: {}", e)))?);
        }

        let (mut ws_stream, _) = connect_async(request).await.map_err(TunnelError::WebSocketError)?;
        
        // 发送初始化会话消息
        let init_msg = if let Some(sid) = *self.session_id.read().await {
            ClientMuxMessage::JoinSession { session_id: sid }
        } else {
            ClientMuxMessage::InitializeSession
        };
        let msg_str = serialize_client_mux_message(&init_msg)?;
        ws_stream.send(Message::Text(msg_str)).await?;

        // 等待会话建立响应
        if let Some(Ok(Message::Text(response))) = ws_stream.next().await {
            if let Ok(ClientMuxMessage::SessionEstablished { session_id }) = deserialize_client_mux_message(&response) {
                // 如果是第一个建立的连接，则设置会话ID
                let mut current_sid = self.session_id.write().await;
                if current_sid.is_none() {
                    *current_sid = Some(session_id);
                    info!("会话已建立，ID: {}", session_id);
                }
                return Ok(ws_stream);
            }
        }
        Err(TunnelError::Other("Session establishment failed".into()))
    }
    
    /// 统一处理来自任意连接的下行消息
    async fn handle_downstream_message(&self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_control_message(&text).await
            }
            Message::Binary(data) => {
                // 在新模型中，我们不需要 ws_index，因为数据总是与 connection_id 关联
                self.handle_data_frame(&data, 0).await 
            }
            Message::Ping(_) => Ok(()), // 由 tungstenite 自动处理
            Message::Pong(_) => Ok(()),
            Message::Close(_) => Err(TunnelError::Other("Connection closed by server".into())),
            Message::Frame(_) => Ok(()), // 忽略原始帧
        }
    }

    async fn handle_local_connection(self: Arc<Self>, tcp_stream: TcpStream) -> Result<()> {
        let connection_id = self.id_generator.next_id();
        info!("处理新的本地连接, 分配 ID: {}", connection_id);

        // --- 1. 轮询选择一个 WebSocket 连接 ---
        let ws_index;
        let sender;

        // 循环直到找到一个可用的 sink
        loop {
            let sinks = self.ws_sinks.read().await;
            if sinks.iter().all(|s| s.is_none()) {
                 return Err(TunnelError::ServiceUnavailable(
                    "没有可用的 WebSocket 连接".into(),
                ));
            }
            let counter = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);
            let index = counter % sinks.len();
            if let Some(s) = sinks[index].clone() {
                ws_index = index;
                sender = s;
                break;
            }
            // 如果槽位为空，则短暂 sleep 后重试
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        info!("Connection {} will use WebSocket connection #{}", connection_id, ws_index);

        // --- 2. Create response waiting channel ---
        let (response_tx, mut response_rx) = mpsc::unbounded_channel::<bool>();
        self.pending_responses.lock().await.insert(connection_id, response_tx);
        debug!("Created response channel for connection {}", connection_id);

        // --- 3. Send connection request ---
        let request_msg = ClientMuxMessage::RequestNewStream {
            connection_id,
            target_addr: self.config.target_addr.clone(),
        };
        let msg_str = serialize_client_mux_message(&request_msg)?;
        sender.send(Message::Text(msg_str)).map_err(|e| TunnelError::Other(format!("Failed to send connection request: {}", e)))?;
        info!("Successfully sent RequestNewStream message to server, connection ID: {}", connection_id);

        debug!("Waiting for server response, connection ID: {}", connection_id);

        // --- 4. 等待服务器响应 ---
        let success = tokio::select! {
            response = response_rx.recv() => {
                match response {
                    Some(success) => {
                        info!("收到服务器响应，连接 {} 结果: {}", connection_id, success);
                        success
                    }
                    None => {
                        error!("等待连接 {} 响应时通道关闭", connection_id);
                        return Err(TunnelError::Other("响应通道关闭".into()));
                    }
                }
            }
            _ = tokio::time::sleep(tokio::time::Duration::from_secs(10)) => {
                error!("等待连接 {} 响应超时", connection_id);
                // 清理等待的响应
                self.pending_responses.lock().await.remove(&connection_id);
                return Err(TunnelError::Other("等待服务器响应超时".into()));
            }
        };

        if !success {
            error!("服务器拒绝了连接 {} 的建连请求", connection_id);
            return Err(TunnelError::Other("服务器拒绝建连请求".into()));
        }

        info!("连接 {} 已成功建立，开始数据转发", connection_id);

        // --- 5. 绑定流和连接 ---
        self.stream_to_ws_map
            .lock()
            .await
            .insert(connection_id, ws_index);

        // --- 6. 设置双向数据转发 ---
        let (read_half, mut write_half) = tcp_stream.into_split();
        let (tx, mut rx) = mpsc::unbounded_channel::<Vec<u8>>();

        // a. 存储下行通道的发送端
        self.active_streams
            .lock()
            .await
            .insert(connection_id, tx);

        // b. 下行数据任务：从 channel 接收数据并写入 TCP
        tokio::spawn(async move {
            while let Some(data) = rx.recv().await {
                if write_half.write_all(&data).await.is_err() {
                    break;
                }
            }
        });

        // c. 上行数据任务：从 TCP 读取数据并转发到 WebSocket
        let self_clone = Arc::clone(&self);
        tokio::spawn(async move {
            let mut buffer = vec![0; DEFAULT_BUFFER_SIZE];
            let mut read_half = tokio::io::BufReader::new(read_half);

            loop {
                match read_half.read(&mut buffer).await {
                    Ok(0) => {
                        info!("本地 TCP 连接 {} 已关闭 (EOF)", connection_id);
                        break;
                    }
                    Ok(n) => {
                        let data = &buffer[..n];
                        if let Err(e) = self_clone
                            .forward_data_to_ws(connection_id, data, ws_index)
                            .await
                        {
                            error!("转发上行数据失败: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!(
                            "从本地 TCP 连接 {} 读取数据时出错: {}",
                            connection_id, e
                        );
                        break;
                    }
                }
            }
            // 清理
            self_clone.remove_connection(connection_id).await;

            // 发送 CloseStream 消息到服务器
            if let Err(e) = self_clone.send_close_stream_message(connection_id, ws_index).await {
                error!("发送 CloseStream 消息失败: {}", e);
            }
        });

        Ok(())
    }

    async fn forward_data_to_ws(
        &self,
        connection_id: ConnectionId,
        data: &[u8],
        ws_index: usize,
    ) -> Result<()> {
        let data_with_id = add_connection_id_prefix(connection_id, data);
        let message = Message::Binary(data_with_id);

        let sinks = self.ws_sinks.read().await;
        if let Some(Some(sender)) = sinks.get(ws_index) {
            sender.send(message).map_err(|e| TunnelError::Other(format!("发送数据失败: {}", e)))?;
            let mut stats = self.stats.write().await;
            stats.bytes_sent += data.len() as u64;
        } else {
            return Err(TunnelError::ConnectionNotFound(connection_id));
        }
        Ok(())
    }

    /// 处理控制消息
    async fn handle_control_message(&self, message: &str) -> Result<()> {
        debug!("处理控制消息: {}", message);
        
        let control_msg: ClientMuxMessage = deserialize_client_mux_message(message)?;
        debug!("成功解析控制消息: {:?}", control_msg);

        match control_msg {
            ClientMuxMessage::NewStreamResponse {
                connection_id,
                success,
                error_message,
            } => {
                info!("收到新流响应: connection_id={}, success={}, error={:?}", 
                      connection_id, success, error_message);
                      
                // 通知等待的连接
                if let Some(tx) = self.pending_responses.lock().await.remove(&connection_id) {
                    debug!("找到等待响应的连接 {}，发送结果: {}", connection_id, success);
                    if let Err(_) = tx.send(success) {
                        warn!("无法发送响应结果给连接 {}", connection_id);
                    }
                } else {
                    warn!("收到未知连接 {} 的响应", connection_id);
                }
            }
            ClientMuxMessage::CloseStream { connection_id } => {
                info!("收到关闭流消息: connection_id={}", connection_id);
                self.remove_connection(connection_id).await;
            }
            ClientMuxMessage::Heartbeat { .. } => {
                debug!("收到心跳消息");
                // 客户端可以忽略心跳消息或者回复
            }
            _ => {
                warn!("收到未处理的控制消息: {:?}", control_msg);
            }
        }
        Ok(())
    }

    /// 处理数据帧
    async fn handle_data_frame(&self, data: &[u8], _ws_index: usize) -> Result<()> {
        let (connection_id, payload) = extract_connection_id_and_payload(data)?;

        let streams = self.active_streams.lock().await;
        if let Some(tx) = streams.get(&connection_id) {
            if tx.send(payload.to_vec()).is_err() {
                error!(
                    "无法将数据发送到本地 TCP 连接 {} (channel closed)",
                    connection_id
                );
                // Channel 关闭意味着接收端（TCP写任务）已经终止，我们需要清理
                drop(streams); // 释放锁
                self.remove_connection(connection_id).await;
            } else {
                let mut stats = self.stats.write().await;
                stats.bytes_received += payload.len() as u64;
            }
        } else {
            warn!("收到未知或已关闭连接 {} 的数据", connection_id);
        }

        Ok(())
    }



    async fn remove_connection(&self, connection_id: ConnectionId) {
        self.stream_to_ws_map.lock().await.remove(&connection_id);
        self.active_streams.lock().await.remove(&connection_id);
        self.pending_responses.lock().await.remove(&connection_id);
    }

    /// 发送 CloseStream 消息到服务器
    async fn send_close_stream_message(&self, connection_id: ConnectionId, ws_index: usize) -> Result<()> {
        let close_msg = ClientMuxMessage::CloseStream { connection_id };
        let msg_str = serialize_client_mux_message(&close_msg)?;

        let sinks = self.ws_sinks.read().await;
        if let Some(Some(sender)) = sinks.get(ws_index) {
            sender.send(Message::Text(msg_str)).map_err(|e| {
                TunnelError::Other(format!("发送 CloseStream 消息失败: {}", e))
            })?;
            debug!("已发送 CloseStream 消息，连接 ID: {}", connection_id);
        } else {
            warn!("无法发送 CloseStream 消息，WebSocket 连接 {} 不可用", ws_index);
        }

        Ok(())
    }
}

pub async fn run_client_mux(config: ClientMuxConfig) -> Result<()> {
    let manager = ClientMuxManager::new(config);

    info!("Starting Client Mux Manager");
    manager.run().await
}

async fn start_local_server(listen_addr: String, manager: Arc<ClientMuxManager>) -> Result<()> {
    let listener = TcpListener::bind(&listen_addr)
        .await
        .map_err(TunnelError::IoError)?;
    info!("客户端 Mux 正在监听: {}", listen_addr);

    loop {
        match listener.accept().await {
            Ok((tcp_stream, _)) => {
                let manager_clone = Arc::clone(&manager);
                tokio::spawn(async move {
                    if let Err(e) = manager_clone.handle_local_connection(tcp_stream).await {
                        error!("处理本地连接时出错: {}", e);
                    }
                });
            }
            Err(e) => {
                error!("接受本地连接失败: {}", e);
            }
        }
    }
} 