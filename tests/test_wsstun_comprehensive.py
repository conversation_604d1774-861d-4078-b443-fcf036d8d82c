#!/usr/bin/env python3
"""
WSSTun Comprehensive Test Script

This script comprehensively validates all functional modes of the wsstun application:
- Server mode
- Client Forward mode (regular and mux)
- Client Proxy mode (regular and mux)
- Hub Service mode
- Various communication scenarios and error handling

Usage:
    python test_wsstun_comprehensive.py [--verbose] [--keep-logs]
"""

import asyncio
import subprocess
import time
import socket
import threading
import json
import requests
import sys
import os
import signal
import tempfile
import logging
import atexit
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from contextlib import asynccontextmanager
import websockets
import aiohttp

# Test configuration
@dataclass
class TestConfig:
    wsstun_binary: str = "../target/debug/wsstun"  # Updated path relative to tests directory
    server_host: str = "127.0.0.1"
    server_port: int = 8060
    test_timeout: int = 30
    verbose: bool = False
    keep_logs: bool = False
    log_dir: str = "./test_logs"

class Colors:
    """Terminal color codes"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class TestResult:
    """Test result class"""
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.skipped = 0
        self.errors: List[str] = []
    
    def add_pass(self):
        self.passed += 1
    
    def add_fail(self, error: str):
        self.failed += 1
        self.errors.append(error)
    
    def add_skip(self, reason: str):
        self.skipped += 1
        self.errors.append(f"SKIPPED: {reason}")
    
    def summary(self) -> str:
        total = self.passed + self.failed + self.skipped
        return f"Total: {total}, Passed: {Colors.GREEN}{self.passed}{Colors.END}, Failed: {Colors.RED}{self.failed}{Colors.END}, Skipped: {Colors.YELLOW}{self.skipped}{Colors.END}"

class ProcessManager:
    """Process manager for starting and managing test processes"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.processes: Dict[str, subprocess.Popen] = {}
        self.log_files: Dict[str, str] = {}
        
        # Create log directory
        os.makedirs(config.log_dir, exist_ok=True)
        
        # Register cleanup function to run on exit
        atexit.register(self.cleanup_all_processes)
        
        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle signals for graceful shutdown"""
        print(f"\n{Colors.YELLOW}Received signal {signum}, cleaning up processes...{Colors.END}")
        self.cleanup_all_processes()
        sys.exit(0)
    
    async def start_process(self, name: str, cmd: List[str], wait_for_ready: Optional[str] = None) -> bool:
        """Start process and wait for it to be ready"""
        log_file = os.path.join(self.config.log_dir, f"{name}.log")
        self.log_files[name] = log_file
        
        try:
            with open(log_file, 'w') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    text=True,
                    preexec_fn=os.setsid if os.name != 'nt' else None
                )
            
            self.processes[name] = process
            
            if wait_for_ready:
                # Wait for process to be ready
                for _ in range(50):  # Wait up to 5 seconds
                    if process.poll() is not None:
                        print(f"{Colors.RED}Process {name} exited unexpectedly{Colors.END}")
                        return False
                    
                    # Check if log file contains ready flag
                    try:
                        with open(log_file, 'r') as f:
                            content = f.read()
                            if wait_for_ready in content:
                                await asyncio.sleep(0.1)  # Extra wait time
                                return True
                    except:
                        pass
                    
                    await asyncio.sleep(0.1)
                
                print(f"{Colors.YELLOW}Process {name} startup timeout{Colors.END}")
                return False
            else:
                await asyncio.sleep(0.5)  # Give process some startup time
                return process.poll() is None
                
        except Exception as e:
            print(f"{Colors.RED}Failed to start process {name}: {e}{Colors.END}")
            return False
    
    def stop_process(self, name: str):
        """Stop specified process"""
        if name in self.processes:
            process = self.processes[name]
            try:
                if os.name == 'nt':
                    # Windows
                    try:
                        process.terminate()
                        process.wait(timeout=3)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        try:
                            process.wait(timeout=2)
                        except subprocess.TimeoutExpired:
                            pass
                else:
                    # Unix-like
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        process.wait()
                    
            except Exception as e:
                print(f"{Colors.YELLOW}Error stopping process {name}: {e}{Colors.END}")
            
            del self.processes[name]
    
    def cleanup_all_processes(self):
        """Cleanup all processes - called on exit"""
        if not self.processes:
            return
            
        print(f"{Colors.YELLOW}Cleaning up {len(self.processes)} test processes...{Colors.END}")
        for name in list(self.processes.keys()):
            self.stop_process(name)
        print(f"{Colors.GREEN}✓ All test processes cleaned up{Colors.END}")
    
    def get_log_content(self, name: str) -> str:
        """Get process log content"""
        if name in self.log_files:
            try:
                with open(self.log_files[name], 'r') as f:
                    return f.read()
            except:
                pass
        return ""

class TestSuite:
    """Main test suite class"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.pm = ProcessManager(config)
        self.result = TestResult()
    
    def log(self, message: str):
        """Print log message"""
        if self.config.verbose:
            print(f"{Colors.CYAN}[LOG]{Colors.END} {message}")
    
    async def run_all_tests(self):
        """Run all tests"""
        print(f"{Colors.BOLD}=== WSSTun Comprehensive Test Start ==={Colors.END}")
        print(f"Binary file: {self.config.wsstun_binary}")
        print(f"Log directory: {self.config.log_dir}")
        print()
        
        # Clean up test logs before starting tests
        print(f"{Colors.YELLOW}Cleaning up test logs before starting...{Colors.END}")
        try:
            import shutil
            if os.path.exists(self.config.log_dir):
                shutil.rmtree(self.config.log_dir)
            # Recreate log directory for the new test run
            os.makedirs(self.config.log_dir, exist_ok=True)
            print(f"{Colors.GREEN}✓ Test logs cleanup completed{Colors.END}")
        except Exception as e:
            print(f"{Colors.YELLOW}Warning: Test logs cleanup failed: {e}{Colors.END}")
        print()
        
        try:
            # 1. Basic functionality tests
            await self.test_binary_exists()
            await self.test_help_command()
            
            # 2. Server mode tests
            await self.test_server_basic()
            await self.test_server_websocket_connection()
            
            # 3. Forward mode tests
            await self.test_forward_basic()
            await self.test_forward_mux()
            await self.test_forward_with_auth()
            
            # 4. Proxy mode tests
            await self.test_proxy_basic()
            await self.test_proxy_mux()
            await self.test_proxy_socks5()
            await self.test_proxy_http()
            
            # 5. Hub Service mode tests
            await self.test_hub_service_basic()
            await self.test_hub_service_heartbeat()
            await self.test_hub_service_mux()
            
            # 6. Comprehensive scenario tests
            await self.test_forward_through_hub()
            await self.test_proxy_through_hub()
            await self.test_multiple_clients()
            
            # 7. Error handling tests
            await self.test_connection_failures()
            await self.test_invalid_configurations()
            
            # 8. Performance and stability tests
            await self.test_high_concurrency()
            await self.test_reconnection()
            
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}Test interrupted by user{Colors.END}")
        except Exception as e:
            print(f"{Colors.RED}Test execution error: {e}{Colors.END}")
        
        # Print test results
        print(f"\n{Colors.BOLD}=== Test Results Summary ==={Colors.END}")
        print(self.result.summary())
        
        if self.result.errors:
            print(f"\n{Colors.RED}Error details:{Colors.END}")
            for error in self.result.errors:
                print(f"  - {error}")
        
        # Keep logs for inspection
        print(f"\n{Colors.CYAN}Log files are preserved in: {self.config.log_dir}{Colors.END}")
        
        return self.result.failed == 0

    async def test_binary_exists(self):
        """Test if binary file exists"""
        test_name = "Binary file existence check"
        self.log(f"Starting test: {test_name}")
        
        try:
            if not os.path.exists(self.config.wsstun_binary):
                self.result.add_fail(f"{test_name}: Binary file does not exist: {self.config.wsstun_binary}")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                return
            
            # Check if executable
            if not os.access(self.config.wsstun_binary, os.X_OK):
                self.result.add_fail(f"{test_name}: Binary file is not executable")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                return
            
            self.result.add_pass()
            print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_help_command(self):
        """Test help command"""
        test_name = "Help command test"
        self.log(f"Starting test: {test_name}")
        
        try:
            result = subprocess.run(
                [self.config.wsstun_binary, "--help"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and "WebSocket Tunnel" in result.stdout:
                self.result.add_pass()
                print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Help command output exception")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_server_basic(self):
        """Test basic Server mode"""
        test_name = "Server basic functionality"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Start server
            cmd = [
                self.config.wsstun_binary,
                "server",
                "--listen", f"{self.config.server_host}:{self.config.server_port}",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("server", cmd, "Starting server mode"):
                # Check if port is listening
                await asyncio.sleep(1)
                if self.is_port_open(self.config.server_host, self.config.server_port):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Server port not listening")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Server startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_server_websocket_connection(self):
        """Test server HTTP health check endpoint"""
        test_name = "Server health check"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Test HTTP health check endpoint
            url = f"http://{self.config.server_host}:{self.config.server_port}/health"
            
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("status") == "healthy":
                                self.result.add_pass()
                                print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                            else:
                                self.result.add_fail(f"{test_name}: Health check status is not healthy")
                                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                        else:
                            self.result.add_fail(f"{test_name}: Health check returned status {response.status}")
                            print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
            except Exception as e:
                self.result.add_fail(f"{test_name}: HTTP health check failed: {str(e)}")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_forward_basic(self):
        """Test basic Forward mode"""
        test_name = "Forward basic functionality"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start a simple TCP server as target
            target_port = 9001
            target_server = await self.start_echo_server(target_port)
            
            # Start forward client
            forward_port = 8001
            cmd = [
                self.config.wsstun_binary,
                "forward",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{forward_port}",
                "--target", f"127.0.0.1:{target_port}",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("forward_client", cmd, "Running in client mode"):
                await asyncio.sleep(1)
                
                # Test data forwarding
                if await self.test_tcp_echo(forward_port, "Hello Forward!"):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Data forwarding test failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Forward client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
            
            # Cleanup
            await self.cleanup_echo_server(target_server, test_name)
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_forward_mux(self):
        """Test Forward Mux mode"""
        test_name = "Forward Mux mode"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start target server
            target_port = 9002
            target_server = await self.start_echo_server(target_port)
            
            # Start forward mux client
            forward_port = 8002
            cmd = [
                self.config.wsstun_binary,
                "--use-mux",
                "forward",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{forward_port}",
                "--target", f"127.0.0.1:{target_port}",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("forward_mux_client", cmd, "Running in client-mux mode"):
                await asyncio.sleep(2)  # mux mode needs more startup time
                
                # Test multiple concurrent connections
                tasks = []
                for i in range(3):
                    tasks.append(self.test_tcp_echo(forward_port, f"Hello Mux {i}!"))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(1 for r in results if r is True)
                
                if success_count >= 2:  # At least 2 successful
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name} (Success {success_count}/3)")
                else:
                    self.result.add_fail(f"{test_name}: Mux concurrent test failed (Success {success_count}/3)")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Forward Mux client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
            
            # Cleanup
            await self.cleanup_echo_server(target_server, test_name)
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_forward_with_auth(self):
        """Test Forward mode with authentication"""
        test_name = "Forward authentication functionality"
        self.log(f"Starting test: {test_name}")
        
        try:
            # This test requires server authentication support, skip for now
            self.result.add_skip("Server authentication functionality not implemented")
            print(f"{Colors.YELLOW}⊘{Colors.END} {test_name} (Skipped)")
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_proxy_basic(self):
        """Test basic Proxy mode"""
        test_name = "Proxy basic functionality"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start proxy client
            proxy_port = 8003
            cmd = [
                self.config.wsstun_binary,
                "proxy",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{proxy_port}",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("proxy_client", cmd, "Running in client mode"):
                await asyncio.sleep(1)
                
                # Check if proxy port is listening
                if self.is_port_open("127.0.0.1", proxy_port):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Proxy port not listening")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Proxy client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_proxy_mux(self):
        """Test Proxy Mux mode"""
        test_name = "Proxy Mux mode"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start proxy mux client
            proxy_port = 8004
            cmd = [
                self.config.wsstun_binary,
                "--use-mux",
                "proxy",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{proxy_port}",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("proxy_mux_client", cmd, "Running in client-mux mode"):
                await asyncio.sleep(2)
                
                # Check if proxy port is listening
                if self.is_port_open("127.0.0.1", proxy_port):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Proxy Mux port not listening")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Proxy Mux client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_proxy_socks5(self):
        """Test SOCKS5 proxy functionality"""
        test_name = "SOCKS5 proxy test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure proxy client is running
            if "proxy_client" not in self.pm.processes and "proxy_mux_client" not in self.pm.processes:
                await self.test_proxy_basic()
            
            # Use SOCKS5 proxy for connection test
            proxy_port = 8003 if "proxy_client" in self.pm.processes else 8004
            
            # Start target server
            target_port = 9003
            target_server = await self.start_echo_server(target_port)
            
            try:
                # Use SOCKS5 proxy connection
                if await self.test_socks5_connection(proxy_port, "127.0.0.1", target_port, "Hello SOCKS5!"):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: SOCKS5 connection test failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            finally:
                await self.cleanup_echo_server(target_server, test_name)
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_proxy_http(self):
        """Test HTTP proxy functionality"""
        test_name = "HTTP proxy test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure proxy client is running
            if "proxy_client" not in self.pm.processes and "proxy_mux_client" not in self.pm.processes:
                await self.test_proxy_basic()
            
            proxy_port = 8003 if "proxy_client" in self.pm.processes else 8004
            
            # Start a simple HTTP server
            http_port = 9004
            http_server = await self.start_http_server(http_port)
            
            try:
                # Use HTTP proxy for connection
                if await self.test_http_proxy_connection(proxy_port, "127.0.0.1", http_port):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: HTTP proxy connection test failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            finally:
                await self.cleanup_http_server(http_server, test_name)
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_hub_service_basic(self):
        """Test basic Hub Service mode"""
        test_name = "Hub Service basic functionality"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start hub service
            service_id = "test-service-001"
            cmd = [
                self.config.wsstun_binary,
                "hub-service",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--service-id", service_id,
                "--heartbeat-interval", "10",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("hub_service", cmd, "Running Hub-Service"):
                await asyncio.sleep(2)
                
                # Check if service registered successfully (via logs)
                log_content = self.pm.get_log_content("hub_service")
                if "service registered" in log_content.lower() or "hub-service" in log_content.lower():
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Hub Service registration failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Hub Service startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_hub_service_heartbeat(self):
        """Test Hub Service heartbeat mechanism"""
        test_name = "Hub Service heartbeat mechanism"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure hub service is running
            if "hub_service" not in self.pm.processes:
                await self.test_hub_service_basic()
            
            # Wait for heartbeat message
            await asyncio.sleep(12)  # Wait for at least one heartbeat cycle
            
            # Check for heartbeat message in logs
            log_content = self.pm.get_log_content("hub_service")
            if "heartbeat" in log_content.lower() or "ping" in log_content.lower():
                self.result.add_pass()
                print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
            else:
                self.result.add_skip("No heartbeat message found in logs")
                print(f"{Colors.YELLOW}⊘{Colors.END} {test_name} (Skipped)")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_hub_service_mux(self):
        """Test Hub Service with --use-mux mode"""
        test_name = "Hub Service MUX mode"
        self.log(f"Starting test: {test_name}")

        try:
            # Stop any existing hub service first
            if "hub_service" in self.pm.processes:
                self.pm.stop_process("hub_service")
                await asyncio.sleep(2)

            # Start hub service with --use-mux
            service_id = "test-service-mux"
            cmd = [
                self.config.wsstun_binary,
                "hub-service",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--service-id", service_id,
                "--heartbeat-interval", "10",
                "--use-mux",
                "--log-level", "debug"
            ]

            await self.pm.start_process("hub_service_mux", cmd)
            await asyncio.sleep(3)  # Wait for startup

            # Check if process is running and logs contain MUX mode indication
            if "hub_service_mux" in self.pm.processes and self.pm.processes["hub_service_mux"].poll() is None:
                log_content = self.pm.get_log_content("hub_service_mux")
                if "Running Hub-Service in MUX mode" in log_content:
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: MUX mode not detected in logs")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Hub service with MUX mode failed to start")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")

            # Stop the mux hub service
            if "hub_service_mux" in self.pm.processes:
                self.pm.stop_process("hub_service_mux")

        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_forward_through_hub(self):
        """Test Forward functionality through Hub"""
        test_name = "Hub Forward comprehensive test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure hub service is running
            if "hub_service" not in self.pm.processes:
                await self.test_hub_service_basic()
            
            # Start target server
            target_port = 9005
            target_server = await self.start_echo_server(target_port)
            
            # Start forward client using hub service
            forward_port = 8005
            service_id = "test-service-001"
            cmd = [
                self.config.wsstun_binary,
                "forward",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{forward_port}",
                "--target", f"127.0.0.1:{target_port}",
                "--service-id", service_id,
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("hub_forward_client", cmd, "Running in client mode"):
                await asyncio.sleep(2)
                
                # Test data forwarding through hub
                if await self.test_tcp_echo(forward_port, "Hello Hub Forward!"):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Hub Forward data forwarding failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Hub Forward client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
            
            # Cleanup
            await self.cleanup_echo_server(target_server, test_name)
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_proxy_through_hub(self):
        """Test Proxy functionality through Hub"""
        test_name = "Hub Proxy comprehensive test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure hub service is running
            if "hub_service" not in self.pm.processes:
                await self.test_hub_service_basic()
            
            # Start proxy client using hub service
            proxy_port = 8006
            service_id = "test-service-001"
            cmd = [
                self.config.wsstun_binary,
                "proxy",
                "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                "--listen", f"127.0.0.1:{proxy_port}",
                "--service-id", service_id,
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("hub_proxy_client", cmd, "Running in client mode"):
                await asyncio.sleep(2)
                
                # Check if proxy port is listening
                if self.is_port_open("127.0.0.1", proxy_port):
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Hub Proxy port not listening")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Hub Proxy client startup failed")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_multiple_clients(self):
        """Test multiple client concurrency"""
        test_name = "Multiple client concurrency test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure server is running
            if "server" not in self.pm.processes:
                await self.test_server_basic()
            
            # Start multiple target servers
            target_servers = []
            client_processes = []
            
            for i in range(2):
                target_port = 9010 + i
                forward_port = 8010 + i
                
                # Start target server
                target_server = await self.start_echo_server(target_port)
                target_servers.append(target_server)
                
                # Start forward client
                cmd = [
                    self.config.wsstun_binary,
                    "forward",
                    "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                    "--listen", f"127.0.0.1:{forward_port}",
                    "--target", f"127.0.0.1:{target_port}",
                    "--log-level", "debug"
                ]
                
                process_name = f"multi_client_{i}"
                if await self.pm.start_process(process_name, cmd, "Running in client mode"):
                    client_processes.append((process_name, forward_port))
            
            await asyncio.sleep(2)
            
            # Test all clients
            success_count = 0
            for process_name, forward_port in client_processes:
                if await self.test_tcp_echo(forward_port, f"Hello Multi {process_name}!"):
                    success_count += 1
            
            if success_count == len(client_processes):
                self.result.add_pass()
                print(f"{Colors.GREEN}✓{Colors.END} {test_name} (Success {success_count}/{len(client_processes)})")
            else:
                self.result.add_fail(f"{test_name}: Multiple client test partially failed (Success {success_count}/{len(client_processes)})")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
            
            # Cleanup
            for i, target_server in enumerate(target_servers):
                await self.cleanup_echo_server(target_server, f"{test_name}_server_{i}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_connection_failures(self):
        """Test connection failure handling"""
        test_name = "Connection failure handling test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Test connection to non-existent server
            cmd = [
                self.config.wsstun_binary,
                "forward",
                "--server", "ws://127.0.0.1:9999",  # Non-existent server
                "--listen", "127.0.0.1:8020",
                "--target", "127.0.0.1:9020",
                "--log-level", "debug"
            ]
            
            if await self.pm.start_process("fail_test_client", cmd):
                await asyncio.sleep(3)
                
                # Check if process is still running (should retry connection)
                if "fail_test_client" in self.pm.processes:
                    process = self.pm.processes["fail_test_client"]
                    if process.poll() is None:
                        # Process is still running, check error handling in logs
                        log_content = self.pm.get_log_content("fail_test_client")
                        if "error" in log_content.lower() or "failed" in log_content.lower():
                            self.result.add_pass()
                            print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
                        else:
                            self.result.add_fail(f"{test_name}: No error handling logs found")
                            print(f"{Colors.RED}✗{Colors.END} {test_name}")
                    else:
                        self.result.add_fail(f"{test_name}: Process exited unexpectedly")
                        print(f"{Colors.RED}✗{Colors.END} {test_name}")
                else:
                    self.result.add_fail(f"{test_name}: Process startup failed")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
            else:
                self.result.add_skip("Connection failure test cannot start")
                print(f"{Colors.YELLOW}⊘{Colors.END} {test_name} (Skipped)")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_invalid_configurations(self):
        """Test invalid configuration handling"""
        test_name = "Invalid configuration handling test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Test invalid listen address
            result = subprocess.run(
                [
                    self.config.wsstun_binary,
                    "forward",
                    "--server", f"ws://{self.config.server_host}:{self.config.server_port}",
                    "--listen", "invalid:address",
                    "--target", "127.0.0.1:8080"
                ],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode != 0:
                self.result.add_pass()
                print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
            else:
                self.result.add_fail(f"{test_name}: Should reject invalid configuration")
                print(f"{Colors.RED}✗{Colors.END} {test_name}")
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_high_concurrency(self):
        """Test high concurrency scenarios"""
        test_name = "High concurrency test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # Ensure there's an available forward client
            forward_port = None
            for name, process in self.pm.processes.items():
                if "forward" in name and process.poll() is None:
                    # Try to get port from process name or logs
                    if "multi_client_0" in name:
                        forward_port = 8010
                        break
                    elif "forward_client" in name:
                        forward_port = 8001
                        break
            
            if forward_port is None:
                self.result.add_skip("No available forward client")
                print(f"{Colors.YELLOW}⊘{Colors.END} {test_name} (Skipped)")
                return
            
            # Start target server
            target_port = 9030
            target_server = await self.start_echo_server(target_port)
            
            try:
                # Create multiple concurrent connections
                tasks = []
                for i in range(10):
                    tasks.append(self.test_tcp_echo(forward_port, f"Concurrent {i}"))
                
                # Wait for all tasks to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(1 for r in results if r is True)
                
                if success_count >= 7:  # At least 70% success
                    self.result.add_pass()
                    print(f"{Colors.GREEN}✓{Colors.END} {test_name} (Success {success_count}/10)")
                else:
                    self.result.add_fail(f"{test_name}: High concurrency test failed (Success {success_count}/10)")
                    print(f"{Colors.RED}✗{Colors.END} {test_name}")
                    
            finally:
                await self.cleanup_echo_server(target_server, test_name)
                
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    async def test_reconnection(self):
        """Test reconnection mechanism"""
        test_name = "Reconnection mechanism test"
        self.log(f"Starting test: {test_name}")
        
        try:
            # This test requires manual disconnection and reconnection, which is complex
            # Skip for now, to be implemented later
            self.result.add_skip("Reconnection test requires more complex scenario setup")
            print(f"{Colors.YELLOW}⊘{Colors.END} {test_name} (Skipped)")
            
        except Exception as e:
            self.result.add_fail(f"{test_name}: {str(e)}")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")

    # Helper methods

    def is_port_open(self, host: str, port: int) -> bool:
        """Check if port is open"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result == 0
        except:
            return False

    async def start_echo_server(self, port: int) -> asyncio.Server:
        """Start a simple echo server"""
        async def handle_client(reader, writer):
            try:
                while True:
                    data = await reader.read(1024)
                    if not data:
                        break
                    writer.write(data)
                    await writer.drain()
            except:
                pass
            finally:
                try:
                    writer.close()
                    await asyncio.wait_for(writer.wait_closed(), timeout=1.0)
                except:
                    pass
        
        server = await asyncio.start_server(handle_client, '127.0.0.1', port)
        return server
    
    async def cleanup_echo_server(self, server: asyncio.Server, test_name: str = ""):
        """Cleanup echo server with timeout"""
        try:
            server.close()
            await asyncio.wait_for(server.wait_closed(), timeout=3.0)
        except asyncio.TimeoutError:
            if test_name:
                print(f"{Colors.YELLOW}Warning: Target server cleanup timeout in {test_name}{Colors.END}")
        except Exception as e:
            if test_name:
                print(f"{Colors.YELLOW}Warning: Target server cleanup error in {test_name}: {e}{Colors.END}")
    
    async def cleanup_http_server(self, server: asyncio.Server, test_name: str = ""):
        """Cleanup HTTP server with timeout"""
        try:
            server.close()
            await asyncio.wait_for(server.wait_closed(), timeout=3.0)
        except asyncio.TimeoutError:
            if test_name:
                print(f"{Colors.YELLOW}Warning: HTTP server cleanup timeout in {test_name}{Colors.END}")
        except Exception as e:
            if test_name:
                print(f"{Colors.YELLOW}Warning: HTTP server cleanup error in {test_name}: {e}{Colors.END}")

    async def start_http_server(self, port: int) -> asyncio.Server:
        """Start a simple HTTP server"""
        async def handle_client(reader, writer):
            try:
                # Read HTTP request
                request = await reader.read(1024)
                
                # Send simple HTTP response
                response = b"HTTP/1.1 200 OK\r\nContent-Length: 13\r\n\r\nHello World!"
                writer.write(response)
                await writer.drain()
            except:
                pass
            finally:
                writer.close()
                await writer.wait_closed()
        
        server = await asyncio.start_server(handle_client, '127.0.0.1', port)
        return server

    async def test_tcp_echo(self, port: int, message: str) -> bool:
        """Test TCP echo connection"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection('127.0.0.1', port),
                timeout=5
            )
            
            writer.write(message.encode())
            await writer.drain()
            
            response = await asyncio.wait_for(reader.read(1024), timeout=5)
            
            writer.close()
            await writer.wait_closed()
            
            return response.decode().strip() == message
            
        except Exception as e:
            self.log(f"TCP echo test failed: {e}")
            return False

    async def test_socks5_connection(self, proxy_port: int, target_host: str, target_port: int, message: str) -> bool:
        """Test SOCKS5 proxy connection"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection('127.0.0.1', proxy_port),
                timeout=5
            )
            
            # Send SOCKS5 handshake
            writer.write(b'\x05\x01\x00')  # SOCKS5, 1 method, no auth
            await writer.drain()
            
            # Wait for server response
            response = await asyncio.wait_for(reader.read(2), timeout=5)
            
            if len(response) == 2 and response[0] == 5:
                # Send connection request
                # SOCKS5 CONNECT request format
                target_ip = socket.inet_aton(target_host)
                connect_request = (
                    b'\x05\x01\x00\x01' +  # SOCKS5, CONNECT, reserved, IPv4
                    target_ip +             # Target IP
                    target_port.to_bytes(2, 'big')  # Target port
                )
                writer.write(connect_request)
                await writer.drain()
                
                # Wait for connection response
                connect_response = await asyncio.wait_for(reader.read(10), timeout=5)
                
                success = (len(connect_response) >= 2 and 
                          connect_response[0] == 5 and 
                          connect_response[1] == 0)  # Success
                
                try:
                    writer.close()
                    await asyncio.wait_for(writer.wait_closed(), timeout=1.0)
                except:
                    pass
                
                return success
            else:
                try:
                    writer.close()
                    await asyncio.wait_for(writer.wait_closed(), timeout=1.0)
                except:
                    pass
                return False
            
        except Exception as e:
            self.log(f"SOCKS5 test failed: {e}")
            return False

    async def test_http_proxy_connection(self, proxy_port: int, target_host: str, target_port: int) -> bool:
        """Test HTTP proxy connection"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection('127.0.0.1', proxy_port),
                timeout=5
            )
            
            # Send HTTP CONNECT request with proper headers
            connect_request = f"CONNECT {target_host}:{target_port} HTTP/1.1\r\nHost: {target_host}:{target_port}\r\nUser-Agent: wsstun-test\r\nProxy-Connection: keep-alive\r\n\r\n"
            writer.write(connect_request.encode())
            await writer.drain()
            
            # Wait for HTTP response
            response = await asyncio.wait_for(reader.read(1024), timeout=5)
            response_str = response.decode('utf-8', errors='ignore')
            
            # Check for successful HTTP response
            success = (b"HTTP" in response and 
                      (b"200" in response or b"Connection established" in response))
            
            try:
                writer.close()
                await asyncio.wait_for(writer.wait_closed(), timeout=1.0)
            except:
                pass
            
            return success
            
        except Exception as e:
            self.log(f"HTTP proxy test failed: {e}")
            return False

    def manual_cleanup(self):
        """Manual cleanup method for user to call if needed"""
        print(f"{Colors.YELLOW}Performing manual cleanup...{Colors.END}")
        self.pm.cleanup_all_processes()
        print(f"{Colors.GREEN}✓ Manual cleanup completed{Colors.END}")

def cleanup_test_logs():
    """Clean up test logs directory only"""
    import shutil
    log_dir = "./test_logs"
    try:
        if os.path.exists(log_dir):
            shutil.rmtree(log_dir)
            print(f"{Colors.GREEN}✓ test_logs directory removed{Colors.END}")
        else:
            print(f"{Colors.YELLOW}test_logs directory does not exist{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}Error removing test_logs directory: {e}{Colors.END}")

async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="WSSTun comprehensive test script")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--keep-logs", action="store_true", help="Keep log files")
    parser.add_argument("--binary", default="../target/debug/wsstun", help="wsstun binary file path")
    parser.add_argument("--server-port", type=int, default=8060, help="Server port")
    parser.add_argument("--timeout", type=int, default=30, help="Test timeout (seconds)")
    parser.add_argument("--cleanup-only", action="store_true", help="Only clean up test_logs directory and exit")
    
    args = parser.parse_args()
    
    # Handle cleanup-only mode
    if args.cleanup_only:
        print(f"{Colors.BOLD}=== WSSTun Test Environment Cleanup ==={Colors.END}")
        cleanup_test_logs()
        print(f"{Colors.GREEN}✓ Cleanup completed{Colors.END}")
        sys.exit(0)
    
    config = TestConfig(
        wsstun_binary=args.binary,
        server_port=args.server_port,
        test_timeout=args.timeout,
        verbose=args.verbose,
        keep_logs=args.keep_logs
    )
    
    # Check if binary file exists
    if not os.path.exists(config.wsstun_binary):
        print(f"{Colors.RED}Error: wsstun binary file not found: {config.wsstun_binary}{Colors.END}")
        print(f"Please compile the project first: {Colors.CYAN}cargo build{Colors.END}")
        sys.exit(1)
    
    test_suite = TestSuite(config)
    
    # Run tests
    success = await test_suite.run_all_tests()
    
    # Print cleanup instructions
    print(f"\n{Colors.BOLD}=== Cleanup Instructions ==={Colors.END}")
    print(f"To clean up test_logs directory, run:")
    print(f"  {Colors.CYAN}python test_wsstun_comprehensive.py --cleanup-only{Colors.END}")
    print(f"Test processes will be automatically cleaned up on script exit.")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main()) 