# WSS Tunnel 中英文转换实施计划

## 概述
本文档提供了WSS Tunnel项目中文注释和日志输出转换为英文的详细实施计划，将转换任务分解为可管理的小任务，确保每次修改都是小规模且可验证的。

## 项目信息
- **项目名称**: wsstun (WebSocket Secure Tunnel)
- **当前版本**: 1.5.3
- **计划开始日期**: 2025年8月16日
- **预计完成日期**: 2025年9月6日（3周）
- **最后更新日期**: 2025年8月16日

## 实施原则

### 1. 小步快跑
- 每个任务应该在1-2小时内完成
- 每个任务完成后立即测试
- 每个任务完成后立即提交代码

### 2. 风险控制
- 优先处理低风险任务
- 每个修改都要有回滚方案
- 重要修改需要代码审查

### 3. 质量保证
- 每个任务完成后必须通过编译
- 每个任务完成后必须通过相关测试
- 每个任务完成后必须验证功能正常

## 详细任务清单

### 阶段1: 高优先级内容转换（第1周）

#### 任务1.1: 转换 `src/proxy_server.rs` 中的错误信息
**目标**: 转换用户可见的错误信息
**风险等级**: 中
**预计时间**: 1小时

**具体步骤**:
1. 备份原文件
2. 转换SOCKS5协议相关错误信息:
   - `"不是有效的SOCKS5版本"` → `"Invalid SOCKS5 version"`
   - `"SOCKS5认证方法不支持"` → `"SOCKS5 authentication method not supported"`
   - `"无效的SOCKS5请求版本"` → `"Invalid SOCKS5 request version"`
   - `"只支持CONNECT命令"` → `"Only CONNECT command is supported"`
   - `"无效的域名"` → `"Invalid domain name"`
   - `"暂不支持IPv6"` → `"IPv6 not supported yet"`
   - `"无效的地址类型"` → `"Invalid address type"`
3. 转换HTTP代理相关错误信息:
   - `"HTTP请求行过长"` → `"HTTP request line too long"`
   - `"无效的HTTP请求行"` → `"Invalid HTTP request line"`
   - `"无效的HTTP请求格式"` → `"Invalid HTTP request format"`
   - `"无效的端口号"` → `"Invalid port number"`
   - `"只支持HTTP URL"` → `"Only HTTP URLs are supported"`
   - `"HTTP请求头过长"` → `"HTTP request headers too long"`
4. 验证编译通过
5. 验证功能正常

**验证方法**:
```bash
cargo build
cargo test
# 运行代理相关测试
```

#### 任务1.2: 转换 `src/proxy_server.rs` 中的重要日志
**目标**: 转换用户可见的重要日志输出
**风险等级**: 中
**预计时间**: 1.5小时

**具体步骤**:
1. 转换连接相关日志:
   - `debug!("成功连接到: {} (解析为: {})", hostname, target_addr);` → `debug!("Successfully connected to: {} (resolved to: {})", hostname, target_addr);`
   - `error!("连接失败 {}: {}", target_addr, e);` → `error!("Connection failed {}: {}", target_addr, e);`
   - `error!("连接超时: {}", target_addr);` → `error!("Connection timeout: {}", target_addr);`
2. 转换协议检测日志:
   - `debug!("SOCKS5认证方法: {:?}", methods);` → `debug!("SOCKS5 authentication methods: {:?}", methods);`
   - `error!("客户端不支持无认证方法");` → `error!("Client does not support no-authentication method");`
3. 验证编译通过
4. 验证功能正常

#### 任务1.3: 转换 `src/hub_service.rs` 中的重要日志
**目标**: 转换Hub服务中的重要日志输出
**风险等级**: 中
**预计时间**: 2小时

**具体步骤**:
1. 转换转发相关日志:
   - `info!("处理转发指令 - 连接到目标: {}", target_addr);` → `info!("Processing forward instruction - connecting to target: {}", target_addr);`
   - `error!("无效的目标地址格式: {}", target_addr);` → `error!("Invalid target address format: {}", target_addr);`
   - `error!("无效的端口号: {}: {}", parts[1], e);` → `error!("Invalid port number: {}: {}", parts[1], e);`
2. 转换连接状态日志:
   - `debug!("完成同步重放 {} 个缓存数据包，客户端: {}", total_packets, connection_id);` → `debug!("Completed synchronous replay of {} cached data packets, client: {}", total_packets, connection_id);`
   - `error!("未找到连接ID {} 的目标连接", connection_id);` → `error!("Target connection not found for connection ID {}", connection_id);`
   - `debug!("目标连接关闭 (EOF) - 连接ID: {}", connection_id);` → `debug!("Target connection closed (EOF) - connection ID: {}", connection_id);`
3. 转换数据转发日志:
   - `error!("从目标连接读取失败: {}", e);` → `error!("Failed to read from target connection: {}", e);`
   - `info!("目标到WebSocket转发任务结束 - 连接ID: {}", connection_id);` → `info!("Target to WebSocket forwarding task ended - connection ID: {}", connection_id);`
4. 验证编译通过
5. 验证功能正常

#### 任务1.4: 转换 `src/hub_service_dynamic.rs` 中的重要日志（第一部分）
**目标**: 转换动态Hub服务中的重要日志输出
**风险等级**: 中
**预计时间**: 2小时

**具体步骤**:
1. 转换服务启动和重连日志:
   - `info!("启动动态隧道模式的Hub服务，服务ID: {}", config.service_id);` → `info!("Starting dynamic tunnel mode Hub service, service ID: {}", config.service_id);`
   - `error!("初始连接失败，服务ID: {}: {}，开始重连流程", config.service_id, e);` → `error!("Initial connection failed, service ID: {}: {}, starting reconnection process", config.service_id, e);`
   - `warn!("收到 {:?} 信号，开始重连流程，服务ID: {}", signal, config.service_id);` → `warn!("Received {:?} signal, starting reconnection process, service ID: {}", signal, config.service_id);`
   - `info!("重连成功，服务ID: {}", config.service_id);` → `info!("Reconnection successful, service ID: {}", config.service_id);`
   - `error!("重连尝试 {} 失败，服务ID: {}: {}", attempt, config.service_id, e);` → `error!("Reconnection attempt {} failed, service ID: {}: {}", attempt, config.service_id, e);`
2. 转换隧道管理日志:
   - `info!("创建新的隧道 {} 为客户端 {}，目标: {}", tunnel_id, client_id, target_addr);` → `info!("Creating new tunnel {} for client {}, target: {}", tunnel_id, client_id, target_addr);`
   - `error!("传统转发隧道任务失败 {}: {}", tunnel_id, e);` → `error!("Legacy forwarding tunnel task failed {}: {}", tunnel_id, e);`
3. 验证编译通过
4. 验证功能正常

#### 任务1.5: 转换 `src/hub_service_dynamic.rs` 中的重要日志（第二部分）
**目标**: 转换动态Hub服务中的其他重要日志输出
**风险等级**: 中
**预计时间**: 2小时

**具体步骤**:
1. 转换控制消息处理日志:
   - `error!("处理控制消息失败: {}", e);` → `error!("Failed to process control message: {}", e);`
   - `debug!("收到控制消息: {:?}", control_msg);` → `debug!("Received control message: {:?}", control_msg);`
   - `debug!("收到传统Hub消息: {:?}", hub_msg);` → `debug!("Received legacy Hub message: {:?}", hub_msg);`
2. 转换隧道任务日志:
   - `info!("开始创建传统隧道任务，客户端ID: {}, 隧道ID: {}, 目标: {}", client_id, tunnel_id, target_addr);` → `info!("Starting legacy tunnel task creation, client ID: {}, tunnel ID: {}, target: {}", client_id, tunnel_id, target_addr);`
   - `info!("成功连接到目标: {}", target_addr);` → `info!("Successfully connected to target: {}", target_addr);`
   - `error!("无法连接到目标 {}: {}", target_addr, e);` → `error!("Unable to connect to target {}: {}", target_addr, e);`
3. 转换数据隧道日志:
   - `info!("成功连接到数据隧道端点: {}", data_ws_url);` → `info!("Successfully connected to data tunnel endpoint: {}", data_ws_url);`
   - `error!("无法连接到数据隧道端点: {}", e);` → `error!("Unable to connect to data tunnel endpoint: {}", e);`
   - `error!("发送数据隧道注册消息失败: {}", e);` → `error!("Failed to send data tunnel registration message: {}", e);`
   - `info!("数据隧道注册成功，客户端ID: {}", client_id);` → `info!("Data tunnel registration successful, client ID: {}", client_id);`
4. 验证编译通过
5. 验证功能正常

### 阶段2: 中优先级内容转换（第2周）

#### 任务2.1: 转换 `src/proxy_server.rs` 中的函数注释
**目标**: 转换函数和方法的文档注释
**风险等级**: 低
**预计时间**: 1.5小时

**具体步骤**:
1. 转换主要函数注释:
   - `/// 从缓存获取或解析DNS` → `/// Get from cache or resolve DNS`
   - `/// 从连接池获取连接或创建新连接` → `/// Get connection from pool or create new connection`
   - `/// 创建新的TCP连接` → `/// Create new TCP connection`
   - `/// 解析代理协议并提取目标地址` → `/// Parse proxy protocol and extract target address`
   - `/// 解析SOCKS5请求` → `/// Parse SOCKS5 request`
   - `/// 解析HTTP代理请求` → `/// Parse HTTP proxy request`
   - `/// 建立到目标服务器的连接（原始版本，保持向后兼容）` → `/// Establish connection to target server (original version, maintain backward compatibility)`
   - `/// 使用连接管理器连接到目标（优化版本）` → `/// Connect to target using connection manager (optimized version)`
2. 验证编译通过
3. 验证文档生成

#### 任务2.2: 转换 `src/hub_service.rs` 中的函数注释
**目标**: 转换Hub服务中的函数注释
**风险等级**: 低
**预计时间**: 1小时

**具体步骤**:
1. 转换主要函数注释:
   - `/// 处理转发指令` → `/// Handle forward instruction`
   - `/// 从目标连接转发数据到WebSocket` → `/// Forward data from target connection to WebSocket`
2. 验证编译通过
3. 验证文档生成

#### 任务2.3: 转换 `src/hub_service_dynamic.rs` 中的函数注释
**目标**: 转换动态Hub服务中的函数注释
**风险等级**: 低
**预计时间**: 1小时

**具体步骤**:
1. 转换主要函数注释:
   - `/// 为传统的转发指令创建隧道（接受目标地址）` → `/// Create tunnel for legacy forward instruction (accepts target address)`
   - `/// 启动控制通道消息处理器（带重连信号）` → `/// Start control channel message handler (with reconnection signal)`
   - `/// 处理控制通道消息` → `/// Handle control channel message`
2. 验证编译通过
3. 验证文档生成

### 阶段3: 低优先级内容转换（第3周）

#### 任务3.1: 转换行内注释
**目标**: 转换代码中的行内注释
**风险等级**: 低
**预计时间**: 2小时

**具体步骤**:
1. 转换 `src/proxy_server.rs` 中的行内注释
2. 转换 `src/hub_service.rs` 中的行内注释
3. 转换 `src/hub_service_dynamic.rs` 中的行内注释
4. 验证编译通过

#### 任务3.2: 最终检查和清理
**目标**: 检查遗漏的中文内容并进行最终清理
**风险等级**: 低
**预计时间**: 1小时

**具体步骤**:
1. 使用正则表达式搜索剩余的中文内容
2. 处理遗漏的翻译
3. 统一术语使用
4. 最终验证

## 质量保证检查清单

### 每个任务完成后必须检查：
- [ ] 代码能正常编译 (`cargo build`)
- [ ] 所有测试通过 (`cargo test`)
- [ ] 没有新的警告 (`cargo clippy`)
- [ ] 代码格式正确 (`cargo fmt --check`)
- [ ] 功能测试通过
- [ ] 翻译准确性检查

### 代码审查检查清单：
- [ ] 术语使用一致
- [ ] 英文语法正确
- [ ] 技术术语准确
- [ ] 上下文适配
- [ ] 保持原意不变

## 风险管理

### 低风险任务
- 转换注释
- 转换调试日志
- 代码格式化

### 中风险任务
- 转换错误信息
- 转换用户可见日志
- 转换警告信息

### 高风险任务
- 修改核心业务逻辑
- 大规模重构

## 回滚策略

### 每个任务的回滚方法：
```bash
# 保存当前修改
git stash

# 回滚到上一个提交
git reset --hard HEAD~1

# 重新开始任务
```

### 紧急回滚：
```bash
git reset --hard HEAD~1
git clean -fd
```

## 总结

这个详细的实施计划将中英文转换任务分解为可管理的小任务，每个任务都有明确的目标、步骤和验证方法。通过分阶段实施，可以确保转换工作的质量和效率，同时最大程度地降低风险。

转换完成后，项目将具备更好的国际化支持，提升代码的专业性和可维护性。
