# WSS Tunnel 中英文转换任务跟踪模板

## 任务基本信息
- **任务编号**: [例如: 1.1]
- **任务名称**: [例如: 转换 `src/proxy_server.rs` 中的错误信息]
- **目标**: [任务目标描述]
- **风险等级**: [低/中/高]
- **预计时间**: [例如: 1小时]
- **实际时间**: [完成后填写]

## 任务状态
- [ ] 未开始
- [ ] 进行中
- [ ] 已完成
- [ ] 已测试
- [ ] 已提交
- [ ] 已审查

## 具体步骤
### 步骤1: 备份和准备
- [ ] 备份原文件
- [ ] 创建工作分支
- **备注**: [如有问题或特殊情况]

### 步骤2: 内容转换
- [ ] 转换错误信息
- [ ] 转换日志输出
- [ ] 转换注释内容
- **备注**: [记录具体转换内容]

### 步骤3: 验证和测试
- [ ] 编译验证通过
- [ ] 功能测试通过
- [ ] 翻译准确性检查
- **备注**: [验证结果]

## 转换内容记录
### 错误信息转换
| 原文 | 译文 | 状态 |
|------|------|------|
| [中文错误信息] | [English error message] | ✅/❌ |

### 日志输出转换
| 原文 | 译文 | 状态 |
|------|------|------|
| [中文日志] | [English log] | ✅/❌ |

### 注释转换
| 原文 | 译文 | 状态 |
|------|------|------|
| [中文注释] | [English comment] | ✅/❌ |

## 验证结果
### 编译验证
- [ ] `cargo check` 通过
- [ ] `cargo build` 通过
- [ ] `cargo clippy` 通过

### 测试验证
- [ ] `cargo test` 通过
- [ ] 相关功能测试通过
- [ ] 性能测试通过（如适用）

### 翻译质量验证
- [ ] 术语使用一致
- [ ] 英文语法正确
- [ ] 技术术语准确
- [ ] 上下文适配
- [ ] 保持原意不变

## 遇到的问题
### 问题1: [问题描述]
- **影响**: [对任务的影响程度]
- **解决方案**: [采取的解决方案]
- **结果**: [解决结果]

### 问题2: [问题描述]
- **影响**: [对任务的影响程度]
- **解决方案**: [采取的解决方案]
- **结果**: [解决结果]

## 修改内容
### 文件修改
- **文件**: [文件路径]
- **修改类型**: [翻译/新增/删除]
- **修改内容**: [简要描述修改内容]

### 代码变更
```rust
// 修改前的代码
// 修改后的代码
```

## 测试结果
### 单元测试
- [ ] 所有相关单元测试通过
- [ ] 新增测试用例（如适用）
- [ ] 测试覆盖率检查

### 集成测试
- [ ] 相关集成测试通过
- [ ] 端到端测试通过（如适用）

### 功能验证
- [ ] 代理功能正常
- [ ] 隧道功能正常
- [ ] 日志输出正确
- [ ] 错误处理正确

## 翻译质量审查
### 术语一致性检查
- [ ] 连接 → Connection
- [ ] 隧道 → Tunnel
- [ ] 转发 → Forward/Forwarding
- [ ] 代理 → Proxy
- [ ] 服务 → Service
- [ ] 客户端 → Client
- [ ] 服务器 → Server
- [ ] 目标 → Target
- [ ] 缓存 → Cache
- [ ] 重连 → Reconnect/Reconnection

### 语法检查
- [ ] 时态使用正确
- [ ] 单复数使用正确
- [ ] 介词使用正确
- [ ] 语序符合英文习惯

### 技术准确性检查
- [ ] 协议术语正确
- [ ] 网络术语正确
- [ ] 编程术语正确
- [ ] 系统术语正确

## 代码审查
### 审查人员
- **审查人**: [姓名]
- **审查日期**: [日期]
- **审查结果**: [通过/需要修改]

### 审查意见
- [ ] 翻译准确性
- [ ] 术语一致性
- [ ] 语法正确性
- [ ] 技术准确性
- [ ] 上下文适配
- [ ] 代码质量

### 审查反馈
- **正面反馈**: [审查人员的正面评价]
- **改进建议**: [审查人员的改进建议]
- **需要修改的地方**: [具体需要修改的地方]

## 提交信息
### Git提交
```bash
git add [修改的文件]
git commit -m "[任务编号] [任务名称]

- 转换错误信息: X处
- 转换日志输出: Y处
- 转换注释内容: Z处
- 验证结果: 编译通过，测试通过"
```

### 分支信息
- **分支名**: [例如: feature/i18n-task-1.1]
- **目标分支**: [例如: main]
- **PR链接**: [如适用]

## 后续任务
### 依赖任务
- [ ] 任务 [任务编号] 需要先完成
- [ ] 任务 [任务编号] 需要先完成

### 后续影响
- [ ] 影响任务 [任务编号]
- [ ] 影响任务 [任务编号]

## 经验总结
### 成功经验
- [经验1]
- [经验2]

### 改进建议
- [建议1]
- [建议2]

### 注意事项
- [注意事项1]
- [注意事项2]

## 完成确认
- [ ] 任务目标已达成
- [ ] 所有验证通过
- [ ] 代码已提交
- [ ] 文档已更新
- [ ] 经验已总结

**完成日期**: [日期]
**完成人**: [姓名]

---

## 使用说明

### 如何填写模板
1. 复制此模板到新的文件
2. 根据具体任务修改任务基本信息
3. 按照步骤执行任务
4. 在每个步骤完成后勾选相应选项
5. 详细记录转换内容
6. 填写验证结果
7. 提交代码审查
8. 总结经验和教训

### 模板使用建议
1. **详细记录**: 每个转换都要详细记录，便于后续参考
2. **及时更新**: 任务状态要及时更新，保持跟踪的准确性
3. **问题记录**: 遇到的问题要详细记录，便于后续避免
4. **质量保证**: 严格按照验证清单进行检查，确保质量
5. **经验总结**: 每个任务完成后要总结经验，提高效率

### 模板维护
- 根据实际使用情况调整模板内容
- 定期更新术语对照表
- 收集使用反馈，持续改进
